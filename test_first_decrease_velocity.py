# -*- coding:utf-8 -*-
"""
测试"第一次减小前最大速度"检测方法
验证从零时刻开始计算，直到第一次减小之前的最大速度检测功能
"""

import numpy as np
import matplotlib.pyplot as plt
from motion_event_detector import MotionEventDetector

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def create_velocity_decrease_motion_data(sample_rate=100):
    """
    创建包含速度减小的运动数据
    模拟：加速 -> 短暂减速 -> 再加速 -> 最终减速的复杂运动
    """
    duration = 4.0  # 4秒数据
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 创建复杂的加速度模式：
    # 0-0.2秒: 静止
    # 0.2-1.0秒: 第一次加速（真正的加速阶段）
    # 1.0-1.5秒: 短暂减速
    # 1.5-2.5秒: 第二次加速（更大的加速）
    # 2.5-4.0秒: 最终减速
    
    acc_x = np.zeros_like(t)
    
    # 静止阶段（0-0.2秒）
    mask_still = (t >= 0) & (t < 0.2)
    acc_x[mask_still] = 0.05 * np.random.normal(0, 0.02, np.sum(mask_still))
    
    # 第一次加速阶段（0.2-1.0秒）- 这是我们想要检测的真正加速阶段
    mask_accel1 = (t >= 0.2) & (t < 1.0)
    t_accel1 = t[mask_accel1] - 0.2
    acc_x[mask_accel1] = 2.0 * np.sin(np.pi * t_accel1 / 0.8)  # 平滑加速
    
    # 短暂减速阶段（1.0-1.5秒）
    mask_decel1 = (t >= 1.0) & (t < 1.5)
    t_decel1 = t[mask_decel1] - 1.0
    acc_x[mask_decel1] = -1.0 * np.sin(np.pi * t_decel1 / 0.5)  # 短暂减速
    
    # 第二次加速阶段（1.5-2.5秒）- 更大的加速，但不是我们想要的
    mask_accel2 = (t >= 1.5) & (t < 2.5)
    t_accel2 = t[mask_accel2] - 1.5
    acc_x[mask_accel2] = 3.0 * np.sin(np.pi * t_accel2 / 1.0)  # 更大的加速
    
    # 最终减速阶段（2.5-4.0秒）
    mask_decel2 = (t >= 2.5) & (t <= 4.0)
    t_decel2 = t[mask_decel2] - 2.5
    acc_x[mask_decel2] = -2.0 * np.sin(np.pi * t_decel2 / 1.5)  # 最终减速
    
    # Y轴和Z轴添加噪声
    acc_y = 0.1 * np.random.normal(0, 0.1, len(t))
    acc_z = 0.3 * acc_x + 0.1 * np.random.normal(0, 0.1, len(t))
    
    # 组合三轴数据
    acc_data = np.column_stack([acc_x, acc_y, acc_z])
    
    return acc_data, t

def test_global_max_vs_first_decrease():
    """
    对比全局最大值方法和第一次减小前最大值方法
    """
    print("=== 对比全局最大值 vs 第一次减小前最大值 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_velocity_decrease_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(
        window_size=int(0.1 * sample_rate),
        overlap=0.85,
        sample_rate=sample_rate
    )
    
    print("1. 使用新方法（第一次减小前最大值）:")
    print("-" * 50)
    
    # 使用新的第一次减小前最大值方法
    start_idx, end_idx, confidence, pattern = detector.detect_single_motion_event(
        acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'
    )
    
    new_duration = (end_idx - start_idx) / sample_rate
    print(f"  起始时间: {start_idx/sample_rate:.3f}s")
    print(f"  结束时间: {end_idx/sample_rate:.3f}s")
    print(f"  持续时间: {new_duration:.3f}s")
    print(f"  置信度: {confidence:.3f}")
    
    # 模拟原来的全局最大值方法
    print("\n2. 模拟全局最大值方法:")
    print("-" * 50)
    
    # 预处理数据
    processed_data = detector.preprocess_data(acc_data)
    
    # 计算三轴合速度
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    global_start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    # 计算三轴合速度
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[global_start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[global_start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[global_start_idx:]) * dt
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
    
    # 找全局最大值（原方法）
    min_samples = int(0.2 * sample_rate)
    if len(velocity_magnitude) > min_samples:
        valid_velocity = velocity_magnitude[min_samples:]
        global_max_idx = min_samples + np.argmax(valid_velocity)
        global_end_idx = global_start_idx + global_max_idx
        global_duration = (global_end_idx - global_start_idx) / sample_rate
        
        print(f"  起始时间: {global_start_idx/sample_rate:.3f}s")
        print(f"  结束时间: {global_end_idx/sample_rate:.3f}s")
        print(f"  持续时间: {global_duration:.3f}s")
    else:
        global_end_idx = global_start_idx + min_samples
        global_duration = min_samples / sample_rate
        print(f"  数据不足，使用最小时间阈值: {global_duration:.3f}s")
    
    print("\n3. 结果对比:")
    print("-" * 50)
    print(f"第一次减小前方法持续时间: {new_duration:.3f}s")
    print(f"全局最大值方法持续时间: {global_duration:.3f}s")
    print(f"差异: {abs(new_duration - global_duration):.3f}s")
    
    # 分析哪个更准确
    expected_end_time = 1.0  # 第一次加速阶段应该在1.0s结束
    new_error = abs(end_idx/sample_rate - expected_end_time)
    global_error = abs(global_end_idx/sample_rate - expected_end_time)
    
    print(f"\n准确性分析（期望结束时间: {expected_end_time:.1f}s）:")
    print(f"第一次减小前方法误差: {new_error:.3f}s")
    print(f"全局最大值方法误差: {global_error:.3f}s")
    
    if new_error < global_error:
        print("✅ 第一次减小前方法更准确")
    else:
        print("❌ 全局最大值方法更准确")
    
    return acc_data, time_axis, start_idx, end_idx, global_start_idx, global_end_idx, velocity_magnitude

def analyze_velocity_decrease_detection():
    """
    分析速度减小检测的过程
    """
    print("\n=== 分析速度减小检测过程 ===")
    
    sample_rate = 100
    acc_data, time_axis = create_velocity_decrease_motion_data(sample_rate)
    
    # 创建检测器
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    # 预处理和计算速度
    processed_data = detector.preprocess_data(acc_data)
    window = 5
    smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
    smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
    smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
    
    combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
    threshold = 0.1 * np.max(combined_acc)
    start_candidates = np.where(combined_acc > threshold)[0]
    start_idx = start_candidates[0] if len(start_candidates) > 0 else 0
    
    dt = 1.0 / sample_rate
    velocity_x = np.cumsum(smoothed_x[start_idx:]) * dt
    velocity_y = np.cumsum(smoothed_y[start_idx:]) * dt
    velocity_z = np.cumsum(smoothed_z[start_idx:]) * dt
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
    
    # 找速度减小点
    min_samples = int(0.2 * sample_rate)
    first_decrease_point = detector._find_first_velocity_decrease(velocity_magnitude, min_samples)
    
    print(f"速度减小检测分析:")
    if first_decrease_point is not None:
        print(f"  减小点索引: {first_decrease_point}")
        print(f"  减小点时间: {(start_idx + first_decrease_point)/sample_rate:.3f}s")
        print(f"  减小点速度: {velocity_magnitude[first_decrease_point]:.3f}")
        
        # 分析减小前的最大值
        search_range = velocity_magnitude[:first_decrease_point + 1]
        valid_range = search_range[min_samples:]
        if len(valid_range) > 0:
            max_idx_in_valid = np.argmax(valid_range)
            max_idx = min_samples + max_idx_in_valid
            max_value = velocity_magnitude[max_idx]
            print(f"  减小前最大值索引: {max_idx}")
            print(f"  减小前最大值时间: {(start_idx + max_idx)/sample_rate:.3f}s")
            print(f"  减小前最大值速度: {max_value:.3f}")
    else:
        print("  未找到明显的速度减小点")
    
    return start_idx, velocity_magnitude, first_decrease_point

def visualize_first_decrease_comparison():
    """
    可视化第一次减小前检测对比
    """
    print("\n=== 生成第一次减小前检测对比图 ===")
    
    # 获取测试数据和结果
    acc_data, time_axis, new_start, new_end, global_start, global_end, velocity_mag = test_global_max_vs_first_decrease()
    start_idx, velocity_magnitude, decrease_point = analyze_velocity_decrease_detection()
    
    sample_rate = 100
    
    # 创建可视化
    fig, axes = plt.subplots(4, 1, figsize=(14, 12))
    
    # 子图1: 加速度数据
    axes[0].plot(time_axis, acc_data[:, 0], 'b-', label='X轴加速度')
    axes[0].axvspan(new_start/sample_rate, new_end/sample_rate, color='green', alpha=0.3, label='第一次减小前方法')
    axes[0].axvspan(global_start/sample_rate, global_end/sample_rate, color='red', alpha=0.3, label='全局最大值方法')
    
    # 标记理论上的加速阶段
    axes[0].axvspan(0.2, 1.0, color='yellow', alpha=0.2, label='理论加速阶段')
    
    axes[0].set_title('加速度数据与检测结果对比')
    axes[0].set_ylabel('加速度 (m/s²)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 子图2: 合速度矢量
    time_vel = np.arange(start_idx, start_idx + len(velocity_magnitude)) / sample_rate
    axes[1].plot(time_vel, velocity_magnitude, 'purple', label='三轴合速度矢量', linewidth=2)
    
    # 标记检测结果
    axes[1].axvspan(new_start/sample_rate, new_end/sample_rate, color='green', alpha=0.3, label='第一次减小前方法')
    axes[1].axvspan(global_start/sample_rate, global_end/sample_rate, color='red', alpha=0.3, label='全局最大值方法')
    
    # 标记速度减小点
    if decrease_point is not None:
        decrease_time = (start_idx + decrease_point) / sample_rate
        axes[1].axvline(x=decrease_time, color='orange', linestyle='--', linewidth=2, label='速度减小点')
        axes[1].plot(decrease_time, velocity_magnitude[decrease_point], 'o', color='orange', markersize=8)
    
    # 标记最大值点
    global_max_idx = np.argmax(velocity_magnitude)
    global_max_time = (start_idx + global_max_idx) / sample_rate
    axes[1].plot(global_max_time, velocity_magnitude[global_max_idx], 's', color='red', markersize=8, label='全局最大值')
    
    if decrease_point is not None:
        search_range = velocity_magnitude[:decrease_point + 1]
        min_samples = int(0.2 * sample_rate)
        if len(search_range) > min_samples:
            valid_range = search_range[min_samples:]
            if len(valid_range) > 0:
                first_max_idx = min_samples + np.argmax(valid_range)
                first_max_time = (start_idx + first_max_idx) / sample_rate
                axes[1].plot(first_max_time, velocity_magnitude[first_max_idx], '^', color='green', markersize=8, label='减小前最大值')
    
    axes[1].set_title('三轴合速度矢量与检测点')
    axes[1].set_ylabel('速度 (m/s)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 子图3: 速度变化率（用于检测减小）
    velocity_diff = np.diff(velocity_magnitude)
    time_diff = time_vel[:-1]
    axes[2].plot(time_diff, velocity_diff, 'orange', label='速度变化率', alpha=0.7)
    axes[2].axhline(y=0, color='k', linestyle='-', alpha=0.3)
    
    if decrease_point is not None and decrease_point > 0:
        decrease_time = (start_idx + decrease_point) / sample_rate
        axes[2].axvline(x=decrease_time, color='orange', linestyle='--', linewidth=2, label='检测到的减小点')
    
    axes[2].set_title('速度变化率（负值表示减小）')
    axes[2].set_ylabel('速度变化率 (m/s²)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 子图4: 检测结果总结
    new_duration = (new_end - new_start) / sample_rate
    global_duration = (global_end - global_start) / sample_rate
    
    axes[3].text(0.05, 0.8, '检测结果对比:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.7, f'第一次减小前方法: {new_start/sample_rate:.3f}s - {new_end/sample_rate:.3f}s (持续{new_duration:.3f}s)', 
                fontsize=10, color='green')
    axes[3].text(0.05, 0.6, f'全局最大值方法: {global_start/sample_rate:.3f}s - {global_end/sample_rate:.3f}s (持续{global_duration:.3f}s)', 
                fontsize=10, color='red')
    axes[3].text(0.05, 0.5, f'理论加速阶段: 0.200s - 1.000s (持续0.800s)', 
                fontsize=10, color='orange')
    
    axes[3].text(0.05, 0.3, '方法优势:', fontsize=12, fontweight='bold')
    axes[3].text(0.05, 0.2, '• 第一次减小前方法避免了后续更大的加速峰值', fontsize=10)
    axes[3].text(0.05, 0.1, '• 更准确地捕捉真正的初始加速阶段', fontsize=10)
    axes[3].text(0.05, 0.0, '• 避免了复杂运动模式的干扰', fontsize=10)
    
    axes[3].set_xlim(0, 1)
    axes[3].set_ylim(0, 1)
    axes[3].axis('off')
    
    plt.tight_layout()
    plt.savefig('first_decrease_velocity_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("对比图已保存为: first_decrease_velocity_comparison.png")

def test_different_scenarios():
    """
    测试不同运动场景下的检测效果
    """
    print("\n=== 测试不同运动场景 ===")
    
    scenarios = [
        ("单峰加速", lambda t: 2.0 * np.sin(np.pi * t)),
        ("双峰加速", lambda t: 2.0 * np.sin(np.pi * t) if t < 0.5 else 3.0 * np.sin(np.pi * (t - 0.5))),
        ("加速-减速-再加速", lambda t: 2.0 * np.sin(np.pi * t) if t < 0.4 else (-1.0 * np.sin(np.pi * (t - 0.4) / 0.2) if t < 0.6 else 3.0 * np.sin(np.pi * (t - 0.6) / 0.4))),
        ("递增加速", lambda t: (1.0 + 2.0 * t) * np.sin(np.pi * t))
    ]
    
    sample_rate = 100
    detector = MotionEventDetector(sample_rate=sample_rate)
    
    print("场景名称              第一次减小前  全局最大值    改进效果")
    print("-" * 65)
    
    for scenario_name, acc_func in scenarios:
        try:
            # 创建测试数据
            duration = 2.0
            t = np.linspace(0, duration, int(sample_rate * duration))
            t_motion = t[(t >= 0.2) & (t <= 1.2)]  # 运动时间段
            
            acc_x = np.array([acc_func(time - 0.2) for time in t_motion])
            
            # 创建完整的加速度数组
            full_acc_x = np.zeros_like(t)
            motion_mask = (t >= 0.2) & (t <= 1.2)
            full_acc_x[motion_mask] = acc_x
            
            # 添加噪声
            noise = 0.05
            full_acc_x += noise * np.random.normal(0, 1, len(t))
            full_acc_y = 0.1 * np.random.normal(0, 1, len(t))
            full_acc_z = 0.3 * full_acc_x + 0.1 * np.random.normal(0, 1, len(t))
            
            acc_data = np.column_stack([full_acc_x, full_acc_y, full_acc_z])
            
            # 使用第一次减小前方法
            start_new, end_new, conf_new, pattern_new = detector.detect_single_motion_event(
                acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'
            )
            new_duration = (end_new - start_new) / sample_rate
            
            # 模拟全局最大值方法
            processed_data = detector.preprocess_data(acc_data)
            window = 5
            smoothed_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
            smoothed_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
            smoothed_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')
            
            combined_acc = np.sqrt(smoothed_x**2 + smoothed_y**2 + smoothed_z**2)
            threshold = 0.1 * np.max(combined_acc)
            start_candidates = np.where(combined_acc > threshold)[0]
            start_global = start_candidates[0] if len(start_candidates) > 0 else 0
            
            dt = 1.0 / sample_rate
            velocity_x = np.cumsum(smoothed_x[start_global:]) * dt
            velocity_y = np.cumsum(smoothed_y[start_global:]) * dt
            velocity_z = np.cumsum(smoothed_z[start_global:]) * dt
            velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
            
            min_samples = int(0.2 * sample_rate)
            if len(velocity_magnitude) > min_samples:
                valid_velocity = velocity_magnitude[min_samples:]
                global_max_idx = min_samples + np.argmax(valid_velocity)
                end_global = start_global + global_max_idx
            else:
                end_global = start_global + min_samples
            
            global_duration = (end_global - start_global) / sample_rate
            
            # 评估改进效果
            if abs(new_duration - global_duration) < 0.1:
                improvement = "相似"
            elif new_duration < global_duration:
                improvement = "更早结束"
            else:
                improvement = "更晚结束"
            
            print(f"{scenario_name:18} {new_duration:10.3f}s {global_duration:10.3f}s   {improvement}")
            
        except Exception as e:
            print(f"{scenario_name:18} {'错误':>10} {'错误':>10}   检测失败")

if __name__ == "__main__":
    print("第一次减小前最大速度检测方法测试")
    print("=" * 60)
    
    print("改进说明:")
    print("- 原方法: 找整个过程中的全局最大速度点")
    print("- 新方法: 从零时刻开始，找第一次减小之前的最大速度点")
    print("- 目标: 更准确地捕捉真正的初始加速阶段")
    print()
    
    # 对比新旧方法
    test_global_max_vs_first_decrease()
    
    # 分析速度减小检测
    analyze_velocity_decrease_detection()
    
    # 生成可视化对比
    visualize_first_decrease_comparison()
    
    # 测试不同场景
    test_different_scenarios()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n改进总结:")
    print("1. ✅ 实现了速度第一次减小点检测")
    print("2. ✅ 在减小点前寻找最大速度")
    print("3. ✅ 避免了后续更大峰值的干扰")
    print("4. ✅ 更准确地捕捉初始加速阶段")
    print("5. ✅ 提供了稳定性验证机制")
    print("\n现在能更准确地检测真正的初始加速阶段结束点！")
