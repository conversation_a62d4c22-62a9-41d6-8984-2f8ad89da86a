"""
将每个运动事件单独可视化为独立的子图
模仿orthogonal_basis_analysis.py中第三幅图的绘制方式

主要修改:
1. 展示范围限制为-2.5到2.5cm
2. 添加可自定义的标准方向虚线指示
3. 支持通过standard_directions参数设置标准方向数组
4. 为每个运动生成独立的子图和总览图
5. 包含统计分析功能
6. 支持最多16个位移图片的展示，采用4x4网格布局
7. 针对16个图片优化了显示尺寸和元素大小

使用方法:
- 修改csv_folder为您的数据路径
- 可选择自定义standard_directions数组来设置标准方向线（现在支持16个方向）
- 运行脚本即可生成所有图像
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import re
from sklearn.decomposition import PCA
from least_squares_plane_fit import find_optimal_projection_plane_least_squares, project_vector_to_plane
from plane_based_coordinates import create_plane_based_coordinates, transform_vectors, read_accelerometer_data, extract_gravity_vector, create_watch_to_world_matrix, transform_to_world_coordinates

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def extract_segment_number(filename):
    """从文件名中提取segment编号"""
    match = re.search(r'segment(\d+)', os.path.basename(filename))
    if match:
        return int(match.group(1))
    return None

def find_orthogonal_basis_on_plane(vectors, plane_normal):
    """
    在拟合平面上找出两个正交的基向量作为矩形平面的长轴和短轴
    """
    # 确保平面法向量是单位向量
    plane_normal = plane_normal / np.linalg.norm(plane_normal)
    
    # 将所有向量投影到平面上
    projected_vectors = np.zeros_like(vectors)
    for i, v in enumerate(vectors):
        dot_product = np.dot(v, plane_normal)
        projected_vectors[i] = v - dot_product * plane_normal
    
    # 过滤掉长度太小的向量
    valid_indices = []
    for i, v in enumerate(projected_vectors):
        if np.linalg.norm(v) > 1e-6:
            valid_indices.append(i)
    
    valid_projected_vectors = projected_vectors[valid_indices]
    
    if len(valid_projected_vectors) < 2:
        # 如果有效向量太少，返回默认基向量
        return np.array([1, 0, 0]), np.array([0, 1, 0]), projected_vectors
    
    # 使用PCA在平面上找出主要方向
    pca = PCA(n_components=2)
    pca.fit(valid_projected_vectors)
    
    # 获取平面上的两个主方向作为长轴和短轴
    major_axis = pca.components_[0]
    minor_axis = pca.components_[1]
    
    # 确保轴向量是单位向量
    major_axis = major_axis / np.linalg.norm(major_axis)
    minor_axis = minor_axis / np.linalg.norm(minor_axis)
    
    # 确保短轴与长轴正交（使用Gram-Schmidt正交化）
    minor_axis = minor_axis - np.dot(minor_axis, major_axis) * major_axis
    minor_axis = minor_axis / np.linalg.norm(minor_axis)
    
    # 检查与平面法向量的正交性并修正
    major_normal_dot = np.dot(major_axis, plane_normal)
    minor_normal_dot = np.dot(minor_axis, plane_normal)
    
    if abs(major_normal_dot) > 1e-6 or abs(minor_normal_dot) > 1e-6:
        major_axis = major_axis - major_normal_dot * plane_normal
        major_axis = major_axis / np.linalg.norm(major_axis)
        
        minor_axis = minor_axis - minor_normal_dot * plane_normal
        minor_axis = minor_axis / np.linalg.norm(minor_axis)
        
        minor_axis = minor_axis - np.dot(minor_axis, major_axis) * major_axis
        minor_axis = minor_axis / np.linalg.norm(minor_axis)
    
    return major_axis, minor_axis, projected_vectors

def visualize_individual_motions(csv_folder, acc_data=None, output_folder=None, standard_directions=None):
    """
    将每个运动事件单独可视化为独立的子图
    
    参数:
    csv_folder: 包含位移数据的文件夹
    acc_data: 加速度数据，用于确定世界坐标系
    output_folder: 输出图像的文件夹路径
    standard_directions: 标准方向数组，每个元素对应一个子图的标准方向
                        格式为[(x1, y1), (x2, y2), ...]，如果为None则使用默认方向
                        数组长度应与运动事件数量匹配，如果不够会循环使用
    
    返回:
    None
    """
    # 使用最小二乘法拟合平面，并获取文件路径信息
    plane_normal, d, displacement_endpoints, source_files = find_optimal_projection_plane_least_squares(csv_folder, return_source_files=True)

    if plane_normal is None:
        print("无法拟合平面")
        return

    # 提取每个向量对应的segment编号
    segment_numbers = []
    for file_path in source_files:
        segment_num = extract_segment_number(file_path)
        segment_numbers.append(segment_num if segment_num is not None else -1)

    # 按segment编号排序，确保1-16的正确顺序
    # 创建索引数组用于排序
    valid_indices = []
    invalid_indices = []

    for i, segment_num in enumerate(segment_numbers):
        if segment_num >= 0:
            valid_indices.append((segment_num, i))
        else:
            invalid_indices.append((-1, i))

    # 按segment编号排序
    valid_indices.sort(key=lambda x: x[0])

    # 重新排列数据
    sorted_indices = [idx for _, idx in valid_indices] + [idx for _, idx in invalid_indices]

    # 重新排序所有相关数组
    displacement_endpoints = np.array([displacement_endpoints[i] for i in sorted_indices])
    source_files = [source_files[i] for i in sorted_indices]
    segment_numbers = [segment_numbers[i] for i in sorted_indices]

    print(f"排序后的segment编号: {segment_numbers}")
    
    # 创建输出文件夹
    if output_folder is None:
        output_folder = os.path.join(csv_folder, "individual_motion_plots")
    os.makedirs(output_folder, exist_ok=True)
    
    # 寻找平面上的矩形长轴和短轴
    major_axis, minor_axis, projected_vectors = find_orthogonal_basis_on_plane(
        displacement_endpoints, plane_normal
    )
    
    # 世界坐标系投影设置（模仿原代码的第三幅图）
    world_y = np.array([1, 0, 0])  # 世界坐标系的Y轴
    world_z = np.array([0, 0, 1])  # 世界坐标系的Z轴

    # 将世界Y轴投影到平面上作为新的X轴
    new_x_proj = world_y - np.dot(world_y, plane_normal) * plane_normal

    # 如果投影后的向量长度接近于0，使用X轴作为替代
    if np.linalg.norm(new_x_proj) < 1e-6:
        world_x = np.array([1, 0, 0])
        new_x_proj = world_x - np.dot(world_x, plane_normal) * plane_normal

    # 归一化X轴投影向量
    new_x_proj = new_x_proj / np.linalg.norm(new_x_proj)

    # 将世界Z轴投影到平面上
    new_y_proj = world_z - np.dot(world_z, plane_normal) * plane_normal

    # 如果投影后的向量长度接近于0，计算与X轴垂直的向量作为Y轴
    if np.linalg.norm(new_y_proj) < 1e-6:
        new_y_proj = np.cross(plane_normal, new_x_proj)
    else:
        # 确保Y轴与X轴正交（使用Gram-Schmidt正交化）
        new_y_proj = new_y_proj - np.dot(new_y_proj, new_x_proj) * new_x_proj

    # 归一化Y轴投影向量
    new_y_proj = new_y_proj / np.linalg.norm(new_y_proj)

    # 创建平面坐标系转换矩阵
    world_plane_matrix = np.vstack([new_x_proj, new_y_proj, plane_normal]).T

    # 转换向量到新的平面坐标系
    world_plane_endpoints = transform_vectors(displacement_endpoints, world_plane_matrix)
    
    # 计算网格大小（根据运动数量动态调整）
    num_motions = len(displacement_endpoints)

    # 设置默认标准方向（如果没有提供）
    if standard_directions is None:
        # 默认标准方向：为每个运动提供一个默认的标准方向
        # 这里可以根据实际需求调整，比如都指向正东方向
        standard_directions = [(2.0, 0)] * num_motions  # 所有都指向正东方向

    # 确保标准方向数组长度匹配运动数量，如果不够则循环使用
    if len(standard_directions) < num_motions:
        # 循环扩展标准方向数组
        extended_directions = []
        for i in range(num_motions):
            extended_directions.append(standard_directions[i % len(standard_directions)])
        standard_directions = extended_directions
    elif len(standard_directions) > num_motions:
        # 如果标准方向过多，只取前面的部分
        standard_directions = standard_directions[:num_motions]

    # 优化网格布局，特别针对16个图片的情况
    if num_motions <= 4:
        cols = 2
        rows = 2
    elif num_motions <= 9:
        cols = 3
        rows = 3
    elif num_motions <= 16:
        cols = 4
        rows = 4
    else:
        # 对于超过16个的情况，使用动态计算
        cols = int(np.ceil(np.sqrt(num_motions)))
        rows = int(np.ceil(num_motions / cols))
    
    # 创建总览图（针对16个图片优化大小）
    if num_motions <= 16:
        # 对于16个或更少的图片，使用较小的子图尺寸以便在一个屏幕内显示
        fig_overview, axes_overview = plt.subplots(rows, cols, figsize=(3*cols, 3*rows))
    else:
        # 对于更多图片，保持原来的大小
        fig_overview, axes_overview = plt.subplots(rows, cols, figsize=(4*cols, 4*rows))
    if num_motions == 1:
        axes_overview = [axes_overview]
    elif rows == 1 or cols == 1:
        axes_overview = axes_overview.flatten()
    else:
        axes_overview = axes_overview.flatten()
    
    # 设置颜色（扩展到16种不同颜色）
    basic_colors = [
        'red', 'blue', 'green', 'orange', 'purple', 'brown', 'yellow', 'gray',
        'olive', 'cyan', 'magenta', 'lime', 'pink', 'navy', 'maroon', 'teal'
    ]
    colors = [basic_colors[i % len(basic_colors)] for i in range(num_motions)]
    
    print(f"\n开始生成 {num_motions} 个独立运动图...")
    
    # 为每个运动事件创建独立的图
    for i, (endpoint, segment_num, color) in enumerate(zip(world_plane_endpoints, segment_numbers, colors)):
        # 创建单独的图
        fig_individual, ax_individual = plt.subplots(1, 1, figsize=(8, 8))
        
        # 绘制当前运动向量
        label = f"Segment {segment_num}" if segment_num >= 0 else f"Motion {i+1}"
        ax_individual.arrow(0, 0, endpoint[0], endpoint[1], 
                           color=color, head_width=0.2, head_length=0.3, 
                           alpha=0.8, linewidth=5)
        
        # 添加起点标记
        ax_individual.plot(0, 0, 'ko', markersize=8, label='起点')
        
        # 添加终点标记
        ax_individual.plot(endpoint[0], endpoint[1], 'o', color=color, 
                          markersize=8, label='终点')
        
        # 添加向量长度和角度信息
        vector_length = np.sqrt(endpoint[0]**2 + endpoint[1]**2)
        vector_angle = np.degrees(np.arctan2(endpoint[1], endpoint[0]))
        if vector_angle < 0:
            vector_angle += 360
        
        # 设置坐标轴范围（统一范围便于比较）
        ax_individual.set_xlim(-2.5, 2.5)  # ±2.5cm
        ax_individual.set_ylim(-2.5, 2.5)  # ±2.5cm
        
        # 添加网格
        ax_individual.grid(True, alpha=0.3)
        ax_individual.set_aspect('equal')
        
        # 添加标准方向箭头（当前运动事件的专属标准方向）
        std_x, std_y = standard_directions[i]
        # 只绘制在显示范围内的标准方向
        if abs(std_x) <= 2.5 and abs(std_y) <= 2.5:
            # 用淡色箭头显示标准方向
            ax_individual.arrow(0, 0, std_x, std_y, 
                               color='lightgray', head_width=0.15, head_length=0.2, 
                               alpha=0.6, linewidth=2, linestyle='--', 
                               label=f'标准方向 ({std_x:.1f}, {std_y:.1f})')
            # 在标准方向端点添加小圆点标记
            ax_individual.plot(std_x, std_y, 'o', color='lightgray', 
                             markersize=5, alpha=0.7)
            
            # 计算实际运动与标准方向的角度偏差
            actual_angle = np.degrees(np.arctan2(endpoint[1], endpoint[0]))
            standard_angle = np.degrees(np.arctan2(std_y, std_x))
            angle_diff = abs(actual_angle - standard_angle)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            # 在图上添加偏差信息
            ax_individual.text(0.02, 0.98, f'角度偏差: {angle_diff:.1f}°', 
                             transform=ax_individual.transAxes, fontsize=10, 
                             verticalalignment='top', bbox=dict(boxstyle='round', 
                             facecolor='wheat', alpha=0.7))
        
        # 设置标题和标签
        ax_individual.set_title(f'{label}\n长度: {vector_length:.2f}cm, 角度: {vector_angle:.1f}°', 
                               fontsize=12, fontweight='bold')
        ax_individual.set_xlabel('X投影 (cm)', fontsize=11)
        ax_individual.set_ylabel('Y投影 (cm)', fontsize=11)
        
        # 添加图例
        ax_individual.legend(loc='upper right')
        
        # 添加坐标轴参考线
        ax_individual.axhline(y=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
        ax_individual.axvline(x=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
        
        # 保存单独的图
        individual_filename = f"motion_{segment_num:03d}.png" if segment_num >= 0 else f"motion_{i+1:03d}.png"
        individual_path = os.path.join(output_folder, individual_filename)
        plt.tight_layout()
        plt.savefig(individual_path, dpi=300, bbox_inches='tight')
        plt.close(fig_individual)
        
        # 在总览图中绘制相同的内容
        if i < len(axes_overview):
            ax_over = axes_overview[i]

            # 根据网格大小调整箭头和标记的大小
            if num_motions <= 16:
                # 对于16个或更少的图片，使用较大的箭头和标记以保持可见性
                head_width = 0.12
                head_length = 0.15
                linewidth = 3
                marker_size = 4
                std_head_width = 0.08
                std_head_length = 0.1
                std_linewidth = 2
                std_marker_size = 3
                title_fontsize = 8
            else:
                # 对于更多图片，使用较小的尺寸
                head_width = 0.08
                head_length = 0.1
                linewidth = 2
                marker_size = 3
                std_head_width = 0.05
                std_head_length = 0.08
                std_linewidth = 1
                std_marker_size = 2
                title_fontsize = 7

            ax_over.arrow(0, 0, endpoint[0], endpoint[1],
                         color=color, head_width=head_width, head_length=head_length,
                         alpha=0.8, linewidth=linewidth)
            ax_over.plot(0, 0, 'ko', markersize=marker_size)
            ax_over.plot(endpoint[0], endpoint[1], 'o', color=color, markersize=marker_size)

            # 添加标准方向箭头（简化版本，每个子图只显示自己的标准方向）
            std_x, std_y = standard_directions[i]
            if abs(std_x) <= 2.5 and abs(std_y) <= 2.5:
                ax_over.arrow(0, 0, std_x, std_y,
                             color='lightgray', head_width=std_head_width, head_length=std_head_length,
                             alpha=0.5, linewidth=std_linewidth, linestyle='--')
                ax_over.plot(std_x, std_y, 'o', color='lightgray',
                            markersize=std_marker_size, alpha=0.6)

            ax_over.set_xlim(-2.5, 2.5)
            ax_over.set_ylim(-2.5, 2.5)
            ax_over.grid(True, alpha=0.3)
            ax_over.set_aspect('equal')
            ax_over.set_title(f'{label}\nL:{vector_length:.1f}cm, A:{vector_angle:.0f}°', fontsize=title_fontsize)
            ax_over.axhline(y=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
            ax_over.axvline(x=0, color='k', linestyle='-', alpha=0.3, linewidth=0.5)
    
    # 隐藏多余的子图
    for j in range(num_motions, len(axes_overview)):
        axes_overview[j].set_visible(False)
    
    # 保存总览图
    overview_path = os.path.join(output_folder, "motions_overview.png")
    plt.tight_layout()
    plt.savefig(overview_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # 生成统计信息
    lengths = []
    angles = []
    x_projections = []
    y_projections = []
    
    for endpoint in world_plane_endpoints:
        length = np.sqrt(endpoint[0]**2 + endpoint[1]**2)
        angle = np.degrees(np.arctan2(endpoint[1], endpoint[0]))
        if angle < 0:
            angle += 360
        
        lengths.append(length)
        angles.append(angle)
        x_projections.append(endpoint[0])
        y_projections.append(endpoint[1])
    
    # 创建统计分析图
    fig_stats, axes_stats = plt.subplots(2, 2, figsize=(12, 10))
    
    # 长度分布
    axes_stats[0, 0].hist(lengths, bins=min(10, num_motions), alpha=0.7, color='skyblue', edgecolor='black')
    axes_stats[0, 0].set_title('位移长度分布')
    axes_stats[0, 0].set_xlabel('长度 (cm)')
    axes_stats[0, 0].set_ylabel('频次')
    axes_stats[0, 0].grid(True, alpha=0.3)
    
    # 角度分布
    axes_stats[0, 1].hist(angles, bins=min(12, num_motions), alpha=0.7, color='lightcoral', edgecolor='black')
    axes_stats[0, 1].set_title('角度分布')
    axes_stats[0, 1].set_xlabel('角度 (度)')
    axes_stats[0, 1].set_ylabel('频次')
    axes_stats[0, 1].grid(True, alpha=0.3)
    
    # X投影分布
    axes_stats[1, 0].hist(x_projections, bins=min(10, num_motions), alpha=0.7, color='lightgreen', edgecolor='black')
    axes_stats[1, 0].set_title('X轴投影分布')
    axes_stats[1, 0].set_xlabel('X投影 (cm)')
    axes_stats[1, 0].set_ylabel('频次')
    axes_stats[1, 0].grid(True, alpha=0.3)
    
    # Y投影分布
    axes_stats[1, 1].hist(y_projections, bins=min(10, num_motions), alpha=0.7, color='plum', edgecolor='black')
    axes_stats[1, 1].set_title('Y轴投影分布')
    axes_stats[1, 1].set_xlabel('Y投影 (cm)')
    axes_stats[1, 1].set_ylabel('频次')
    axes_stats[1, 1].grid(True, alpha=0.3)
    
    # 保存统计图
    stats_path = os.path.join(output_folder, "motion_statistics.png")
    plt.tight_layout()
    plt.savefig(stats_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计结果
    print(f"\n运动事件统计分析:")
    print(f"总计运动事件数: {num_motions}")
    print(f"平均位移长度: {np.mean(lengths):.2f} ± {np.std(lengths):.2f} cm")
    print(f"位移长度范围: {np.min(lengths):.2f} - {np.max(lengths):.2f} cm")
    print(f"平均角度: {np.mean(angles):.1f} ± {np.std(angles):.1f} 度")
    print(f"X轴投影平均: {np.mean(x_projections):.2f} ± {np.std(x_projections):.2f} cm")
    print(f"Y轴投影平均: {np.mean(y_projections):.2f} ± {np.std(y_projections):.2f} cm")
    print(f"\n标准方向配置信息:")
    print(f"运动事件数量: {num_motions}")
    print(f"标准方向数量: {len(standard_directions)}")
    if len(standard_directions) < num_motions:
        print(f"注意: 标准方向不足，将循环使用前 {len(standard_directions)} 个方向")
    elif len(standard_directions) > num_motions:
        print(f"注意: 标准方向过多，只使用前 {num_motions} 个方向")
    
    print(f"\n每个运动事件的标准方向分配:")
    for i, (std_x, std_y) in enumerate(standard_directions):
        std_length = np.sqrt(std_x**2 + std_y**2)
        std_angle = np.degrees(np.arctan2(std_y, std_x))
        if std_angle < 0:
            std_angle += 360
        segment_display = f"Segment {segment_numbers[i]}" if segment_numbers[i] >= 0 else f"Motion {i+1}"
        print(f"  {segment_display}: 标准方向({std_x:.1f}, {std_y:.1f})cm, 长度{std_length:.1f}cm, 角度{std_angle:.0f}°")
    print(f"\n独立运动图已保存至: {output_folder}")
    print(f"- 总览图: motions_overview.png")
    print(f"- 统计图: motion_statistics.png")
    print(f"- 单独运动图: motion_XXX.png")
    print(f"\n说明: 每个子图中的淡灰色虚线箭头表示该运动事件的专属标准方向")
    print(f"      可用于观察实际运动轨迹与预期标准方向的偏差程度")
    print(f"      角度偏差信息显示在每个子图的左上角")

if __name__ == "__main__":
    # 指定包含CSV文件的文件夹路径
    csv_folder = r"E:\Document\user001\7.25\Session_20250725_225125 - 副本\output_world_coordinates11"
    
    # 寻找加速度计数据文件
    acce_files = glob.glob(os.path.join(csv_folder, "acce_data*.csv"))
    if not acce_files:
        acce_files = glob.glob(os.path.join(csv_folder, "grav_data*.csv"))
    
    if not acce_files:
        print("未找到加速度计数据文件")
        acc_data = None
    else:
        # 读取第一个加速度计数据文件
        acc_data = read_accelerometer_data(acce_files[0])
        print(f"读取加速度计数据: {len(acc_data)} 个样本")
    
    # 输出文件夹路径
    output_folder = os.path.join(csv_folder, "individual_motions")
    
    # 定义标准方向数组（每个元素对应一个运动事件的专属标准方向）
    # 格式：[(x1, y1), (x2, y2), ...] 单位：cm
    # 重要：第i个元素是第i个运动事件的标准方向
    # 如果运动数量超过这里定义的数量，会循环使用这些方向
    custom_standard_directions = [
        (0, 2.0),       # 运动事件1的标准方向：正东方向，2cm
        (0, -2.0),      # 运动事件2的标准方向：正北方向，2cm
        (1.5, 1.5),      # 运动事件3的标准方向：正西方向，2cm
        (-1.5, -1.5),       # 运动事件4的标准方向：正南方向，2cm
        (2.0, 0),    # 运动事件5的标准方向：东北方向，约2.1cm
        (-2.0, 0),     # 运动事件6的标准方向：东南方向，约2.1cm
        (1.5, -1.5),   # 运动事件7的标准方向：西北方向，约2.1cm
        (-1.5, 1.5),    # 运动事件8的标准方向：西南方向，约2.1cm
        (0, -2.0),       # 运动事件9的标准方向：正东方向，2.2cm
        (0, 2.0),      # 运动事件10的标准方向：正北方向，2.2cm
        (-1.5, -1.5),      # 运动事件11的标准方向：正西方向，2.2cm
        (1.5, 1.5),       # 运动事件12的标准方向：正南方向，2.2cm
        (-2.0, 0),    # 运动事件13的标准方向：东北方向，约2.5cm
        (2.0, 0),     # 运动事件14的标准方向：东南方向，约2.5cm
        (-1.5, 1.5),   # 运动事件15的标准方向：西北方向，约2.5cm
        (1.5, -1.5),    # 运动事件16的标准方向：西南方向，约2.5cm
        # 如果有更多运动事件，会从第1个方向开始循环使用
    ]
    
    print("标准方向配置说明:")
    print("- 现在支持最多16个运动事件的可视化")
    print("- 每个运动事件都有一个专属的标准方向")
    print("- 标准方向以淡灰色虚线箭头显示在对应的子图中")
    print("- 可以通过比较实际运动轨迹（彩色箭头）和标准方向（灰色箭头）来评估偏差")
    print("- 角度偏差会显示在每个子图的左上角")
    print("- 16个图片采用4x4网格布局，优化了显示效果\n")
    
    # 执行分析和可视化
    visualize_individual_motions(
        csv_folder, 
        acc_data=acc_data,
        output_folder=output_folder,
        standard_directions=custom_standard_directions
    )
    
    print("\n独立运动可视化分析完成!")
