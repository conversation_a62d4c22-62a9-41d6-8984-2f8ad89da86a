# -*- coding:utf-8 -*-
"""
作者：${何志想}
日期：2024年08月15日
单一运动事件检测：从连续加速度数据中精确分割出单次运动事件
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import butter, filtfilt, find_peaks
from scipy.stats import zscore
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import os
import re

plt.rcParams['font.sans-serif'] = ['SimHei'] 
plt.rcParams['axes.unicode_minus'] = False 

class MotionEventDetector:
    """单一运动事件检测器 - 优化版本，专注于X和Z轴的波形变化"""
    
    def __init__(self, window_size=10, overlap=0.5, sample_rate=100):
        """
        初始化运动事件检测器
        
        参数:
            window_size: 窗口大小(采样点数)
            overlap: 窗口重叠比例(0-1)
            sample_rate: 采样率(Hz)
        """
        self.window_size = window_size
        self.overlap = overlap
        self.sample_rate = sample_rate
        self.step_size = int(window_size * (1 - overlap))
        
        # 检测参数
        self.energy_threshold = 0.4  # 能量阈值(相对于最大值的比例)
        self.dynamic_threshold_factor = 0.35  # 动态阈值因子
        self.min_event_duration = 0.2  # 最小事件持续时间(秒)
        self.max_event_duration = 8.0  # 最大事件持续时间(秒)
        self.pre_event_buffer = 0.1  # 事件前缓冲时间(秒)
        self.post_event_buffer = 0.1  # 事件后缓冲时间(秒)
          # 优化版参数设置
        self.axis_weights = [0.45, 0.1, 0.45]  # X轴和Z轴权重高，Y轴权重低
        self.wave_shape_score_weight = 0.6  # 波形形状得分权重
        self.center_preference_weight = 0.4  # 事件中心位置偏好权重
        self.edge_ratio_threshold = 0.25  # 边缘区域比例阈值(相对于数据总长度)
        
        # 积分法参数
        self.last_main_axis = 0  # 最后检测到的主轴
        
        # 鞍状检测参数
        self.saddle_detection = True  # 启用鞍状检测
        self.trend_window_size = 5  # 趋势分析窗口大小
        self.saddle_sensitivity = 0.3  # 鞍状灵敏度 (0-1)
        
        # 边界优化参数
        self.edge_threshold = 0.2  # 边缘阈值，低于此值的区域被视为静态
        self.edge_optimization = True  # 启用边界优化
        self.edge_window = 3  # 边缘平滑窗口
    
    def preprocess_data(self, data):
        """
        预处理加速度数据
        
        参数:
            data: 三轴加速度数据 [N, 3]
            
        返回:
            处理后的数据
        """
        # 1. 去除重力影响 (高通滤波)
        filtered_data = self._apply_highpass_filter(data, cutoff=0.5)
        
        # 2. 去除高频噪声 (低通滤波)
        filtered_data = self._apply_lowpass_filter(filtered_data, cutoff=10.0)
        
        return filtered_data
    
    def _apply_highpass_filter(self, data, cutoff=0.5):
        """应用高通滤波器去除重力影响"""
        nyq = 0.5 * self.sample_rate
        normal_cutoff = cutoff / nyq
        b, a = butter(3, normal_cutoff, btype='high')
        
        filtered_data = np.zeros_like(data)
        for i in range(data.shape[1]):
            filtered_data[:, i] = filtfilt(b, a, data[:, i])
            
        return filtered_data
        
    def _apply_lowpass_filter(self, data, cutoff=10.0):
        """应用低通滤波器去除高频噪声"""
        nyq = 0.5 * self.sample_rate
        normal_cutoff = cutoff / nyq
        b, a = butter(3, normal_cutoff, btype='low')
        
        filtered_data = np.zeros_like(data)
        for i in range(data.shape[1]):
            filtered_data[:, i] = filtfilt(b, a, data[:, i])
            
        return filtered_data
    
    def extract_features(self, data):
        """
        提取特征 - 增强版，添加信号趋势分析和轴权重
        """
        n_samples = len(data)
        n_windows = (n_samples - self.window_size) // self.step_size + 1
        
        # 创建特征数组
        features = np.zeros(n_windows)
        features_by_axis = np.zeros((n_windows, data.shape[1]))  # 分轴特征
        
        # 创建额外的趋势和变化率特征
        feature_trend = np.zeros(n_windows)
        
        # 滑动窗口计算特征
        for i in range(n_windows):
            start_idx = i * self.step_size
            end_idx = start_idx + self.window_size
            window_data = data[start_idx:end_idx]
            
            # 分别计算每个轴的特征
            for axis in range(data.shape[1]):
                # 计算单轴特征(使用绝对值的均值，更好地捕捉波动)
                axis_feature = np.mean(np.abs(window_data[:, axis]))
                features_by_axis[i, axis] = axis_feature
            
            # 基于轴权重组合特征
            features[i] = np.sum(features_by_axis[i] * self.axis_weights)
            
            # 计算变化趋势 (用于鞍状检测)
            if i >= 2:
                feature_trend[i] = features[i] - features[i-2]
        
        # 归一化特征
        features = (features - np.min(features)) / (np.max(features) - np.min(features) + 1e-10)
        
        return features, feature_trend, features_by_axis
    
    def _calculate_wave_shape_score(self, data_segment, axis_index):
        """
        计算波形形状得分并识别特定模式的波形
        高分：先增后减或先减后增的波形，或者减增减、增减增模式的前半部分
        低分：单调或杂乱的波形
        
        参数:
            data_segment: 数据段
            axis_index: 轴索引(0=X, 1=Y, 2=Z)
            
        返回:
            wave_shape_score: 0-1之间的分数，越高表示波形越符合预期
            wave_pattern: 识别的波形模式类型
            optimal_end_idx: 推荐的结束索引（对于减增减和增减增模式）
        """
        if len(data_segment) < 5:
            return 0.5, "unknown", None  # 数据点太少，返回中性分数
            
        # 分段长度(每段约占总长度的1/4)
        segment_length = max(3, len(data_segment) // 4)
        
        # 提取指定轴的数据
        axis_data = data_segment[:, axis_index]
        
        # 计算每个段的平均值
        seg1_mean = np.mean(axis_data[:segment_length])
        seg2_mean = np.mean(axis_data[segment_length:2*segment_length])
        seg3_mean = np.mean(axis_data[2*segment_length:3*segment_length])
        seg4_mean = np.mean(axis_data[3*segment_length:])
        
        # 计算各段之间的差值
        diff1 = seg2_mean - seg1_mean
        diff2 = np.mean(axis_data[segment_length:3*segment_length]) - seg1_mean
        diff3 = seg4_mean - seg3_mean
        
        # 根据0值判断增减
        sign1 = 1 if diff1 > 0 else -1
        sign2 = 1 if diff2 > 0 else -1
        sign3 = 1 if diff3 > 0 else -1
        
        # 检测波形模式
        
        # 减增减模式：第一段到第二段下降，第二段到第三段上升，第三段到第四段下降
        decrease_increase_decrease = (sign1 == -1 and sign2 == 1 and sign3 == -1)
        
        # 增减增模式：第一段到第二段上升，第二段到第三段下降，第三段到第四段上升
        increase_decrease_increase = (sign1 == 1 and sign2 == -1 and sign3 == 1)
        
        # 检测先增后减的模式
        inc_dec_pattern = (sign1 == 1 and sign3 == -1)
        
        # 检测先减后增的模式
        dec_inc_pattern = (sign1 == -1 and sign3 == 1)
        
        # 计算中间段变化的显著性
        middle_change = abs(diff2)
        
        # 为特殊模式确定最佳结束点
        optimal_end_idx = None
        
        # 判断波形类型
        if decrease_increase_decrease:
            # 减增减模式 - 推荐在第三段结束处截断
            optimal_end_idx = 3 * segment_length
            wave_pattern = "decrease_increase_decrease"
            pattern_score = 0.8 + 0.2 * min(1.0, middle_change / 0.5)
        elif increase_decrease_increase:
            # 增减增模式 - 推荐在第三段结束处截断
            optimal_end_idx = 3 * segment_length
            wave_pattern = "increase_decrease_increase"
            pattern_score = 0.8 + 0.2 * min(1.0, middle_change / 0.5)
        elif inc_dec_pattern:
            # 先增后减模式
            wave_pattern = "increase_decrease"
            pattern_score = 0.7 + 0.3 * min(1.0, middle_change / 0.5)
        elif dec_inc_pattern:
            # 先减后增模式
            wave_pattern = "decrease_increase"
            pattern_score = 0.7 + 0.3 * min(1.0, middle_change / 0.5)
        else:
            # 不符合预期模式，但有一定变化
            if abs(diff1) > 0.1 or abs(diff2) > 0.1 or abs(diff3) > 0.1:
                wave_pattern = "irregular_with_changes"
                pattern_score = 0.5  # 中性分数
            else:
                # 几乎没有变化的平坦信号
                wave_pattern = "flat"
                pattern_score = 0.3
        
        return pattern_score, wave_pattern, optimal_end_idx
    
    def _calculate_center_position_score(self, start_idx, end_idx, total_length):
        """
        计算事件在整个信号中的位置得分
        中心位置得分高，靠近边缘得分低
        
        参数:
            start_idx: 事件起始索引
            end_idx: 事件结束索引
            total_length: 信号总长度
            
        返回:
            position_score: 0-1之间的分数，越高表示位置越好
        """
        # 计算事件中心在整个信号中的相对位置(0-1)
        event_center = (start_idx + end_idx) / 2
        relative_center = event_center / total_length
        
        # 在信号中间位置(0.5)得分最高，向两边递减
        center_score = 1.0 - 2.0 * abs(relative_center - 0.5)
        
        # 根据事件是否位于边缘区域调整得分
        edge_ratio = self.edge_ratio_threshold
        if relative_center < edge_ratio or relative_center > (1.0 - edge_ratio):
            # 处于边缘区域，降低得分
            center_score *= 0.5
            
        return max(0.1, center_score)  # 确保至少有一个基础得分
    
    def _evaluate_segment_quality(self, data, start_idx, end_idx):
        """
        评估事件段的质量，综合考虑波形形状和位置
        
        参数:
            data: 完整数据
            start_idx: 事件起始样本索引
            end_idx: 事件结束样本索引
            
        返回:
            quality_score: 0-1之间的得分，越高质量越好
        """
        # 提取事件段数据
        event_segment = data[start_idx:end_idx]
        
        if len(event_segment) < 5:
            return 0.5  # 事件太短，给中性分数
            
        # 计算X轴和Z轴的波形形状得分
        x_wave_score, _, _ = self._calculate_wave_shape_score(event_segment, 0)
        z_wave_score, _, _ = self._calculate_wave_shape_score(event_segment, 2)
        
        # 组合波形得分 (根据轴权重)
        wave_shape_score = (x_wave_score * self.axis_weights[0] + 
                           z_wave_score * self.axis_weights[2]) / (self.axis_weights[0] + self.axis_weights[2])
        
        # 计算事件位置得分
        position_score = self._calculate_center_position_score(start_idx, end_idx, len(data))
        
        # 计算总得分
        total_score = (wave_shape_score * self.wave_shape_score_weight + 
                      position_score * self.center_preference_weight)
        
        return total_score
    
    def detect_event(self, features, data=None):
        """
        检测单一运动事件 - 增强型，支持波形和位置评估
        
        参数:
            features: 特征向量序列
            data: 原始数据(用于波形评估)
            
        返回:
            start_idx: 事件开始索引
            end_idx: 事件结束索引
        """
        # 计算特征趋势
        feature_trend = np.zeros_like(features)
        feature_trend[1:] = features[1:] - features[:-1]
        
        # 1. 计算自适应阈值
        dynamic_threshold = np.mean(features) + self.dynamic_threshold_factor * np.std(features)
        absolute_threshold = self.energy_threshold * np.max(features)
        threshold = min(dynamic_threshold, absolute_threshold)  # 使用更宽松的阈值
        
        # 2. 基于阈值找到潜在事件段
        above_threshold = features > threshold
        
        # 如果没有超过阈值的点，使用最高能量点作为事件
        if not np.any(above_threshold):
            peak_idx = np.argmax(features)
            min_idx = max(0, int(peak_idx - self.min_event_duration * self.sample_rate / self.step_size))
            max_idx = min(len(features)-1, int(peak_idx + self.min_event_duration * self.sample_rate / self.step_size))
            return self._optimize_event_edges(features, min_idx, max_idx)
        
        # 3. 找到连续段
        segments = self._find_continuous_segments(above_threshold)
        
        # 4. 如果有多个事件段，评估每个段的质量
        if len(segments) > 1 and data is not None:
            # 计算每个段的质量得分
            segment_scores = []
            
            for start, end in segments:
                # 将窗口索引转换为样本索引
                sample_start, sample_end = self.convert_window_idx_to_sample_idx(start, end)
                
                # 确保索引在有效范围内
                sample_start = max(0, sample_start)
                sample_end = min(len(data), sample_end)
                
                # 评估段的质量
                quality_score = self._evaluate_segment_quality(data, sample_start, sample_end)
                
                # 计算段的能量
                energy = np.sum(features[start:end])
                
                # 综合考虑质量得分和能量
                combined_score = quality_score * 0.7 + (energy / np.max(features)) * 0.3
                
                segment_scores.append(combined_score)
            
            # 选择最高得分的段
            best_segment_idx = np.argmax(segment_scores)
            start_idx, end_idx = segments[best_segment_idx]
            
        elif len(segments) > 0:
            # 仅有一个段或无法评估质量，选择能量最高的段
            segment_energies = [np.sum(features[start:end]) for start, end in segments]
            best_segment_idx = np.argmax(segment_energies)
            start_idx, end_idx = segments[best_segment_idx]
        else:
            # 没有找到任何段
            peak_idx = np.argmax(features)
            half_window = int(self.min_event_duration * self.sample_rate / self.step_size / 2)
            start_idx = max(0, peak_idx - half_window)
            end_idx = min(len(features) - 1, peak_idx + half_window)
            return self._optimize_event_edges(features, start_idx, end_idx)
            
        # 5. 检测鞍状分布
        if self.saddle_detection:
            is_saddle, saddle_start, peak_idx = self._detect_saddle_pattern(features, feature_trend)
            
            # 如果检测到鞍状分布且鞍部起始点早于当前起始点，则更新起始点
            if is_saddle and saddle_start < start_idx:
                start_idx = saddle_start
        
        # 6. 扩展事件边界以包含前后的缓冲区
        buffer_samples = int(self.pre_event_buffer * self.sample_rate / self.step_size)
        post_buffer_samples = int(self.post_event_buffer * self.sample_rate / self.step_size)
        
        start_idx = max(0, start_idx - buffer_samples)
        end_idx = min(len(features), end_idx + post_buffer_samples)
        
        # 7. 优化边缘以去除平滑部分
        start_idx, end_idx = self._optimize_event_edges(features, start_idx, end_idx)
        
        # 8. 确保事件在允许的持续时间范围内
        event_duration = (end_idx - start_idx) * self.step_size / self.sample_rate
        
        if event_duration < self.min_event_duration:
            # 扩展太短的事件
            additional_samples = int((self.min_event_duration - event_duration) 
                                     * self.sample_rate / self.step_size / 2)
            start_idx = max(0, start_idx - additional_samples)
            end_idx = min(len(features), end_idx + additional_samples)
            
        elif event_duration > self.max_event_duration:
            # 裁剪太长的事件
            target_samples = int(self.max_event_duration * self.sample_rate / self.step_size)
            
            # 找出能量最集中的区域，但确保包含主峰值
            peak_idx = start_idx + np.argmax(features[start_idx:end_idx])
            
            # 从峰值向两边扩展
            half_target = target_samples // 2
            new_start = max(0, peak_idx - half_target)
            new_end = min(len(features), new_start + target_samples)
            
            # 如果右边界超出范围，调整左边界
            if new_end > len(features):
                new_start = max(0, len(features) - target_samples)
                new_end = len(features)
            
            start_idx = new_start
            end_idx = new_end
            
            # 再次优化边缘
            start_idx, end_idx = self._optimize_event_edges(features, start_idx, end_idx)
        
        return start_idx, end_idx
    
    def _find_continuous_segments(self, binary_vector):
        """找到二值向量中的连续段"""
        segments = []
        in_segment = False
        start_idx = 0
        
        for i, value in enumerate(binary_vector):
            if value and not in_segment:
                # 找到段的开始
                in_segment = True
                start_idx = i
            elif not value and in_segment:
                # 找到段的结束
                in_segment = False
                segments.append((start_idx, i))
        
        # 处理最后一个段
        if in_segment:
            segments.append((start_idx, len(binary_vector)))
            
        return segments
    
    def convert_window_idx_to_sample_idx(self, window_start_idx, window_end_idx):
        """将窗口索引转换为样本索引"""
        sample_start_idx = window_start_idx * self.step_size
        sample_end_idx = (window_end_idx - 1) * self.step_size + self.window_size
        
        return sample_start_idx, sample_end_idx
    
    def _detect_saddle_pattern(self, features, feature_trend):
        """检测鞍状分布模式"""
        n = len(features)
        if n < 10:  # 数据点太少，无法检测复杂模式
            return False, None, None
        
        # 查找主峰位置
        main_peak_idx = np.argmax(features)
        
        # 向前搜索鞍部起始
        saddle_start = main_peak_idx
        min_before_peak = np.inf
        min_before_peak_idx = main_peak_idx
        
        # 从峰值向左扫描至少1/4的数据长度，寻找趋势变化
        search_range = max(int(n/4), 10)
        search_start = max(0, main_peak_idx - search_range)
        
        for i in range(main_peak_idx - 1, search_start, -1):
            if features[i] < min_before_peak:
                min_before_peak = features[i]
                min_before_peak_idx = i
            
            # 检测趋势突然变化点
            if i > 1 and feature_trend[i] < 0 and feature_trend[i-1] > 0:
                if (features[main_peak_idx] - features[i]) > self.saddle_sensitivity * (features[main_peak_idx] - np.min(features)):
                    saddle_start = i
                    break
        
        # 如果没找到明显的趋势变化点，则使用最小值前的点
        if saddle_start == main_peak_idx and min_before_peak_idx < main_peak_idx:
            # 查找最小值之前的平稳区域
            for i in range(min_before_peak_idx, 0, -1):
                if features[i] > features[min_before_peak_idx] * (1 + self.saddle_sensitivity):
                    saddle_start = i
                    break
        
        # 找不到明显的鞍部起点，则尝试使用能量阈值的一半作为辅助判断
        if saddle_start == main_peak_idx:
            half_energy_threshold = np.min(features) + (np.max(features) - np.min(features)) * 0.2
            for i in range(main_peak_idx - 1, 0, -1):
                if features[i] < half_energy_threshold:
                    saddle_start = i + 1
                    break
        
        # 如果还是没找到合适的起点，就返回峰值的较早时刻
        if saddle_start == main_peak_idx:
            saddle_start = max(0, main_peak_idx - int(n/10))
        
        return True, saddle_start, main_peak_idx
    
    def _optimize_event_edges(self, features, start_idx, end_idx):
        """
        优化事件边缘，避免包含平滑的非运动部分
        
        参数:
            features: 特征序列
            start_idx: 初始起始索引
            end_idx: 初始结束索引
            
        返回:
            优化后的起始和结束索引
        """
        if not self.edge_optimization:
            return start_idx, end_idx
        
        if end_idx <= start_idx:
            return start_idx, end_idx
            
        # 计算事件内部的平均特征值和标准差
        event_features = features[start_idx:end_idx]
        event_mean = np.mean(event_features)
        peak_value = np.max(event_features)
        
        # 边缘判断阈值 - 动态计算
        edge_threshold = peak_value * self.edge_threshold
        
        # 1. 优化起始边界 - 向前寻找第一个超过阈值的点
        optimized_start = start_idx
        for i in range(start_idx, min(end_idx - 1, len(features) - 1)):
            # 使用小窗口平滑判断
            if i + self.edge_window < len(features):
                window_avg = np.mean(features[i:i+self.edge_window])
                if window_avg > edge_threshold:
                    optimized_start = i
                    break
        
        # 2. 优化结束边界 - 向后寻找最后一个超过阈值的点
        optimized_end = end_idx
        for i in range(end_idx - 1, optimized_start + 1, -1):
            if i - self.edge_window >= 0:
                window_avg = np.mean(features[i-self.edge_window:i])
                if window_avg > edge_threshold:
                    optimized_end = i
                    break
        
        # 确保优化后的事件持续时间符合最小要求
        if (optimized_end - optimized_start) * self.step_size / self.sample_rate < self.min_event_duration:            # 如果太短，则保持原始边界
            return start_idx, end_idx
            
        return optimized_start, optimized_end
        
    def detect_single_motion_event(self, acc_data, only_acceleration_phase=True, use_integration=True, detection_method='velocity'):
        """
        从加速度数据中检测单一运动事件

        参数:
            acc_data: 三轴加速度数据 [N, 3]
            only_acceleration_phase: 是否只检测加速阶段（默认是）
            use_integration: 是否使用积分方法（简化检测）
            detection_method: 检测方法 ('velocity' 或 'displacement')
                            'velocity': 使用速度最大值点检测加速部分
                            'displacement': 使用位移最大值点检测加速部分

        返回:
            start_idx: 事件开始样本索引
            end_idx: 事件结束样本索引
            event_confidence: 事件检测置信度(0-1)
            wave_pattern: 检测到的波形模式类型(字符串)
        """
        # 使用积分法简化检测
        if use_integration:
            start_idx, end_idx, confidence, main_axis = self.detect_motion_by_integration(
                acc_data, only_acceleration_phase, detection_method
            )

            # 确定波形模式
            if main_axis == 0:
                wave_pattern = f"X轴主导运动({detection_method}方法)"
            else:
                wave_pattern = f"Z轴主导运动({detection_method}方法)"

            # 保存主轴信息，以便可视化
            self.last_main_axis = main_axis

            return start_idx, end_idx, confidence, wave_pattern
        
        # 以下是原始的鞍状检测算法
        # 1. 预处理数据
        processed_data = self.preprocess_data(acc_data)
        
        # 2. 提取特征 (包括趋势分析)
        features, feature_trend, features_by_axis = self.extract_features(processed_data)
        
        # 3. 检测事件 (包括鞍状检测和波形评估)
        window_start_idx, window_end_idx = self.detect_event(features, processed_data)
        
        # 4. 转换为样本索引
        sample_start_idx, sample_end_idx = self.convert_window_idx_to_sample_idx(
            window_start_idx, window_end_idx)
        
        # 确保索引在有效范围内
        sample_start_idx = max(0, sample_start_idx)
        sample_end_idx = min(len(acc_data), sample_end_idx)
        
        # 5. 对检测到的事件段进行波形分析
        event_data = acc_data[sample_start_idx:sample_end_idx]
        main_pattern = "unknown"
        
        # 检查事件数据是否足够长
        if len(event_data) > 20:  # 至少需要一定数量的样本进行有效分析
            # 对X轴和Z轴进行波形分析
            x_score, x_pattern, x_optimal_end = self._calculate_wave_shape_score(event_data, 0)
            z_score, z_pattern, z_optimal_end = self._calculate_wave_shape_score(event_data, 2)
            
            # 获取主要关注轴的波形信息 (根据得分选择X轴或Z轴)
            if x_score > z_score:
                main_pattern = x_pattern
                optimal_end = x_optimal_end
                main_axis = 0  # X轴为主轴
            else:
                main_pattern = z_pattern
                optimal_end = z_optimal_end
                main_axis = 2  # Z轴为主轴
            
            # 如果检测到特殊波形模式(减增减/增减增)，调整事件结束边界
            if main_pattern in ["decrease_increase_decrease", "increase_decrease_increase"] and optimal_end is not None:
                # 计算新的结束索引位置
                new_end_idx = sample_start_idx + optimal_end
                
                # 确保新的结束索引在合理范围内
                if new_end_idx > sample_start_idx + 10:  # 确保事件长度足够
                    print(f"检测到特殊波形模式: {main_pattern}，调整事件边界")
                    sample_end_idx = new_end_idx
                    
            # 【新增】如果只需要加速阶段，则寻找加速-减速分界点
            if only_acceleration_phase:
                # 使用主轴数据查找加速-减速分界点
                boundary_idx, boundary_confidence = self._find_acceleration_deceleration_boundary(event_data, main_axis)
                
                # 将相对于事件的索引转换为绝对索引
                acceleration_end_idx = sample_start_idx + boundary_idx
                
                # 确保分界点在有效范围内
                acceleration_end_idx = min(sample_end_idx, acceleration_end_idx)
                acceleration_end_idx = max(sample_start_idx + 5, acceleration_end_idx)  # 至少包含5个点
                
                # 更新事件结束边界，仅保留加速阶段
                print(f"找到加速-减速分界点，可信度: {boundary_confidence:.2f}")
                print(f"加速阶段: [{sample_start_idx}:{acceleration_end_idx}], 总事件: [{sample_start_idx}:{sample_end_idx}]")
                
                # 更新结束索引，只保留加速阶段
                sample_end_idx = acceleration_end_idx
                main_pattern = f"acceleration_phase_of_{main_pattern}"
        else:
            main_pattern = "too_short"
            
        # 6. 计算置信度
        event_section = features[window_start_idx:window_end_idx]
        non_event_section = np.concatenate([
            features[:window_start_idx], 
            features[window_end_idx:]
        ]) if window_start_idx > 0 or window_end_idx < len(features) else np.array([0])
        
        # 检查事件区域和非事件区域的特征差异
        if len(event_section) > 0 and len(non_event_section) > 0:
            event_mean = np.mean(event_section)
            non_event_mean = np.mean(non_event_section)
            
            # 计算信噪比作为基础置信度
            if non_event_mean > 0:
                snr = event_mean / non_event_mean
                base_confidence = min(1.0, (snr - 1) / 5)  # 归一化到0-1
            else:
                base_confidence = 1.0
                
            # 计算事件质量得分
            quality_score = self._evaluate_segment_quality(
                processed_data, sample_start_idx, sample_end_idx)
            
            # 综合置信度
            confidence = 0.6 * base_confidence + 0.4 * quality_score
        else:
            confidence = 0.5
            main_pattern = "uncertain"
        
        return sample_start_idx, sample_end_idx, confidence, main_pattern
    def visualize_detection(self, acc_data, start_idx, end_idx, features=None, save_path=None, show_acc_dec_boundary=True):
        """
        可视化检测结果，包括优化后的边界和波形分析
        
        参数:
            acc_data: 加速度数据
            start_idx: 事件起始索引
            end_idx: 事件结束索引
            features: 特征向量
            save_path: 保存路径
            show_acc_dec_boundary: 是否显示加速-减速分界点
        """
        # 创建包含5个子图的画布
        plt.figure(figsize=(15, 15))
        
        # 计算时间轴
        time = np.arange(len(acc_data)) / self.sample_rate
        
        # 创建刻度
        tick_interval = int(self.sample_rate)  # 每1秒一个刻度
        ticks = range(0, len(acc_data), tick_interval)
        tick_labels = [f"{t/self.sample_rate:.1f}s" for t in ticks]
        
        # 事件起止时间
        event_time_start = start_idx / self.sample_rate
        event_time_end = end_idx / self.sample_rate
        
        # 如果需要标记加速-减速分界点，则计算分界点
        acc_dec_boundary = None
        if show_acc_dec_boundary and start_idx < end_idx:
            # 提取完整事件，不只是检测到的加速阶段
            event_data = acc_data[start_idx:min(len(acc_data), start_idx + 2 * (end_idx - start_idx))]
            if len(event_data) > 10:
                # 找到X轴和Z轴的主轴
                x_score, _, _ = self._calculate_wave_shape_score(event_data, 0)
                z_score, _, _ = self._calculate_wave_shape_score(event_data, 2)
                main_axis = 0 if x_score > z_score else 2
                
                # 寻找加速-减速分界点
                boundary_idx, _ = self._find_acceleration_deceleration_boundary(event_data, main_axis)
                acc_dec_boundary = start_idx + boundary_idx
          # 1. 特征分析图 (子图1)
        plt.subplot(5, 1, 1)
        
        # 如果有特征数据，绘制特征曲线
        if (features is not None):
            feature_time = np.arange(len(features)) * self.step_size / self.sample_rate
            plt.plot(feature_time, features, 'b-')
            
            # 标记检测到的事件
            window_start_idx = int(start_idx / self.step_size)
            window_end_idx = int(end_idx / self.step_size)
            
            if window_end_idx >= len(features):
                window_end_idx = len(features) - 1
                
            feature_time_start = window_start_idx * self.step_size / self.sample_rate
            feature_time_end = window_end_idx * self.step_size / self.sample_rate
            
            plt.axvspan(feature_time_start, feature_time_end, color='r', alpha=0.3)
            
            # 标注事件起止点
            plt.plot(feature_time_start, features[window_start_idx], 'ro', markersize=6)
            plt.plot(feature_time_end, features[window_end_idx], 'ro', markersize=6)
            
            # 添加最优边界标注
            plt.annotate('优化边界', xy=(feature_time_start, features[window_start_idx]),
                        xytext=(feature_time_start, max(features) * 0.7),
                        arrowprops=dict(facecolor='black', shrink=0.05, width=1))
            
            # 找到事件中的峰值用于标注
            if window_end_idx > window_start_idx:
                peak_idx = window_start_idx + np.argmax(features[window_start_idx:window_end_idx])
                plt.annotate('运动峰值', xy=(feature_time[peak_idx], features[peak_idx]),
                            xytext=(feature_time[peak_idx], max(features) * 0.85),
                            arrowprops=dict(facecolor='black', shrink=0.05, width=1))
                
            # 添加过滤边缘的阈值线
            if self.edge_optimization:
                edge_threshold = np.max(features) * self.edge_threshold
                plt.axhline(y=edge_threshold, color='g', linestyle='--', linewidth=1, 
                           label=f'边缘阈值 ({self.edge_threshold:.2f})')
                
            # 标记加速-减速分界点
            if acc_dec_boundary is not None:
                acc_dec_window_idx = int(acc_dec_boundary / self.step_size)
                if 0 <= acc_dec_window_idx < len(features):
                    acc_dec_time = acc_dec_window_idx * self.step_size / self.sample_rate
                    plt.axvline(x=acc_dec_time, color='m', linestyle='-.', linewidth=2)
                    plt.annotate('加速-减速分界', xy=(acc_dec_time, features[min(acc_dec_window_idx, len(features)-1)]),
                                xytext=(acc_dec_time, max(features) * 0.55),
                                arrowprops=dict(facecolor='magenta', shrink=0.05, width=1))
                
            plt.legend()
        else:
            # 否则绘制加速度模值
            acc_magnitude = np.sqrt(np.sum(acc_data**2, axis=1))
            plt.plot(time, acc_magnitude, 'k-')
            plt.axvspan(event_time_start, event_time_end, color='r', alpha=0.3)
            
            # 标记加速-减速分界点
            if acc_dec_boundary is not None:
                acc_dec_time = acc_dec_boundary / self.sample_rate
                plt.axvline(x=acc_dec_time, color='m', linestyle='-.', linewidth=2)
                plt.annotate('加速-减速分界', xy=(acc_dec_time, acc_magnitude[min(acc_dec_boundary, len(acc_magnitude)-1)]),
                            xytext=(acc_dec_time, max(acc_magnitude) * 0.55),
                            arrowprops=dict(facecolor='magenta', shrink=0.05, width=1))
            
        plt.title('Movement Feature Analysis - 优化边界检测')
        plt.xlabel('')
        plt.ylabel('特征值')
        plt.grid(True)
          # 2. 波形分析图 (X和Z轴叠加) (子图2)
        plt.subplot(5, 1, 2)
        
        # 提取X轴和Z轴的事件数据
        if start_idx < end_idx and start_idx < len(acc_data):
            # 获取更长的事件数据用于分析和显示
            extended_end_idx = min(len(acc_data), start_idx + 2 * (end_idx - start_idx))
            full_event_x = acc_data[start_idx:extended_end_idx, 0]
            full_event_z = acc_data[start_idx:extended_end_idx, 2]
            
            # 获取检测到的事件数据
            event_x = acc_data[start_idx:end_idx, 0]
            event_z = acc_data[start_idx:end_idx, 2]
            
            # 归一化到[-1, 1]范围 - 使用完整事件数据进行归一化
            norm_factor_x = np.max(np.abs(full_event_x)) + 1e-10
            norm_factor_z = np.max(np.abs(full_event_z)) + 1e-10
            
            norm_full_event_x = full_event_x / norm_factor_x
            norm_full_event_z = full_event_z / norm_factor_z
            
            # 创建事件时间轴
            full_event_time = np.arange(len(full_event_x)) / self.sample_rate
            detected_event_length = len(event_x)
            
            # 绘制归一化的X和Z轴波形 - 绘制完整事件
            plt.plot(full_event_time, norm_full_event_x, 'r-', alpha=0.4, linewidth=1)
            plt.plot(full_event_time, norm_full_event_z, 'b-', alpha=0.4, linewidth=1)
            
            # 突出显示检测到的事件部分
            plt.plot(full_event_time[:detected_event_length], norm_full_event_x[:detected_event_length], 
                    'r-', label='X轴 (归一化)', linewidth=2)
            plt.plot(full_event_time[:detected_event_length], norm_full_event_z[:detected_event_length], 
                    'b-', label='Z轴 (归一化)', linewidth=2)
            
            # 添加4等分点的垂直线
            for i in range(1, 4):
                division = i * detected_event_length // 4
                plt.axvline(x=full_event_time[division], color='gray', linestyle='--', alpha=0.5)
            
            # 计算并显示波形得分
            x_score, x_pattern, _ = self._calculate_wave_shape_score(acc_data[start_idx:end_idx], 0)
            z_score, z_pattern, _ = self._calculate_wave_shape_score(acc_data[start_idx:end_idx], 2)
            
            plt.text(0.02, 0.95, f'X轴波形得分: {x_score:.2f} ({x_pattern})', transform=plt.gca().transAxes, 
                    bbox=dict(facecolor='white', alpha=0.8), fontsize=8)
            plt.text(0.02, 0.88, f'Z轴波形得分: {z_score:.2f} ({z_pattern})', transform=plt.gca().transAxes,
                    bbox=dict(facecolor='white', alpha=0.8), fontsize=8)
            
            # 标记检测到的事件结束边界
            plt.axvline(x=full_event_time[detected_event_length-1], color='red', linestyle='-', linewidth=2)
            plt.annotate('检测边界', xy=(full_event_time[detected_event_length-1], 0),
                         xytext=(full_event_time[detected_event_length-1] + 0.05, -0.8),
                         arrowprops=dict(facecolor='red', shrink=0.05, width=1))
            
            # 如果有加速-减速分界点且在范围内，则标记
            if acc_dec_boundary is not None and start_idx <= acc_dec_boundary < extended_end_idx:
                # 计算相对位置
                rel_idx = acc_dec_boundary - start_idx
                if 0 <= rel_idx < len(full_event_time):
                    plt.axvline(x=full_event_time[rel_idx], color='cyan', linestyle='-.', linewidth=2)
                    plt.annotate('加速-减速分界', xy=(full_event_time[rel_idx], 0),
                                xytext=(full_event_time[rel_idx] - 0.05, 0.8),
                                arrowprops=dict(facecolor='cyan', shrink=0.05, width=1),
                                color='blue', fontweight='bold')
            
            # 标记零点
            for i, (data, color) in enumerate([(norm_full_event_x, 'red'), (norm_full_event_z, 'blue')]):
                # 找到主要的过零点
                zero_crossings = np.where(np.diff(np.signbit(data)))[0]
                if len(zero_crossings) > 0:
                    # 仅显示事件区域内的过零点
                    for z in zero_crossings:
                        if z < len(full_event_time):
                            plt.plot(full_event_time[z], 0, 'o', color=color, markersize=4, alpha=0.5)
        
        plt.title('波形分析 (蓝色垂直线标记加速-减速分界点)')
        plt.xlabel('时间 (秒)')
        plt.ylabel('归一化振幅')
        plt.grid(True)
        plt.legend()
          # 3-5. 三轴加速度原始数据图 (子图3-5)
        axis_names = ['X轴 (高权重)', 'Y轴 (低权重)', 'Z轴 (高权重)']
        colors = ['r', 'g', 'b']
        
        for i in range(3):
            plt.subplot(5, 1, i+3)
            
            # 绘制加速度数据
            plt.plot(time, acc_data[:, i], color=colors[i])
            
            # 调整x轴刻度
            plt.xticks(ticks, tick_labels, rotation=-35)
            
            # 标记检测到的事件
            plt.axvspan(event_time_start, event_time_end, color='r', alpha=0.3)
            
            # 添加开始和结束标记线
            plt.axvline(x=event_time_start, color='m', linestyle='--', linewidth=1)
            plt.axvline(x=event_time_end, color='m', linestyle='--', linewidth=1)
            
            # 标记加速-减速分界点
            if acc_dec_boundary is not None:
                acc_dec_time = acc_dec_boundary / self.sample_rate
                plt.axvline(x=acc_dec_time, color='cyan', linestyle='-.', linewidth=2)
                
                # 只在X轴或Z轴上添加标注（主要轴）
                if i == 0 or i == 2:  # X轴或Z轴
                    # 计算加速度值
                    acc_value = acc_data[min(acc_dec_boundary, len(acc_data)-1), i]
                    if i == 0:  # X轴，上方标注
                        plt.annotate('加速-减速分界', xy=(acc_dec_time, acc_value),
                                    xytext=(acc_dec_time, max(acc_data[:, i]) * 0.9),
                                    arrowprops=dict(facecolor='cyan', shrink=0.05, width=1),
                                    color='blue', fontweight='bold')
            
            # 计算轴权重文本
            weight_text = f'权重: {self.axis_weights[i]:.2f}'
            plt.text(0.02, 0.9, weight_text, transform=plt.gca().transAxes,
                    bbox=dict(facecolor='white', alpha=0.8))
            
            # 添加过零点指示
            if i == 0 or i == 2:  # 只在X和Z轴上标记过零点
                # 找到主要的过零点
                zero_crossings = np.where(np.diff(np.signbit(acc_data[:, i])))[0]
                if len(zero_crossings) > 0:
                    # 仅显示事件区域内的过零点
                    event_zero_crossings = [z for z in zero_crossings if start_idx <= z <= end_idx]
                    for z in event_zero_crossings:
                        z_time = z / self.sample_rate
                        plt.plot(z_time, 0, 'ko', markersize=4, alpha=0.5)
            
            plt.grid(True)
            plt.title(f'{axis_names[i]}')
            plt.xlabel('时间 (秒)' if i == 2 else '')
            plt.ylabel('加速度 (m/s²)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300)
            print(f"可视化结果已保存至: {save_path}")
        
        plt.show()
        plt.close()

    def _find_acceleration_deceleration_boundary(self, data, axis_index=0):
        """
        在运动事件中找到加速和减速的分界点（加速度从正变为负的点）
        
        参数:
            data: 运动事件数据段
            axis_index: 用于分析的轴索引（默认使用X轴）
            
        返回:
            boundary_idx: 加速-减速分界点在数据段中的索引
            confidence: 分界点判断的可信度(0-1)
        """
        # 确保数据足够长
        if len(data) < 10:
            return len(data) // 2, 0.3  # 数据太短，返回中间点作为分界点，低可信度
        
        # 获取指定轴的预处理加速度数据
        processed_data = self.preprocess_data(data)
        axis_data = processed_data[:, axis_index]
        
        # 1. 平滑数据以减少噪声影响
        window_size = min(11, len(axis_data) // 5)  # 适应性窗口大小，确保奇数
        if window_size % 2 == 0:
            window_size += 1
            
        # 应用滑动平均进行平滑
        smooth_data = np.convolve(axis_data, np.ones(window_size)/window_size, mode='valid')
        
        # 处理边缘，使平滑后的数据长度与原数据一致
        pad_size = (len(axis_data) - len(smooth_data)) // 2
        smooth_data = np.pad(smooth_data, (pad_size, len(axis_data) - len(smooth_data) - pad_size), 'edge')
        
        # 2. 寻找可能的零点（加速度符号变化点）
        zero_crossings = []
        for i in range(1, len(smooth_data)):
            # 从正到负的过零点（加速转为减速）
            if smooth_data[i-1] > 0 and smooth_data[i] <= 0:
                zero_crossings.append((i, abs(smooth_data[i-1] - smooth_data[i])))  # 保存过零点和变化幅度
        
        # 如果没有找到明显的过零点，尝试寻找极值点
        if not zero_crossings:
            # 寻找加速度峰值点，作为加速阶段结束的估计点
            peaks, _ = find_peaks(smooth_data)
            
            if peaks.size > 0:
                # 选择最大的峰值
                main_peak = peaks[np.argmax(smooth_data[peaks])]
                # 峰值后一段时间可视为加速结束
                boundary_idx = min(len(smooth_data) - 1, main_peak + len(smooth_data) // 10)
                return boundary_idx, 0.6
            else:
                # 找不到峰值，使用数据中点作为分界点
                return len(data) // 2, 0.4
        
        # 3. 如果有多个零点，选择最显著的一个
        if len(zero_crossings) > 1:
            # 按变化幅度排序，选择变化最明显的零点
            zero_crossings.sort(key=lambda x: x[1], reverse=True)
            
            # 考虑运动模式，一般加速部分约占整体的前30%-60%
            reasonable_boundaries = []
            total_length = len(smooth_data)
            
            for idx, amp in zero_crossings:
                rel_pos = idx / total_length
                # 更倾向于位于数据中段的零点
                if 0.3 <= rel_pos <= 0.7:
                    reasonable_boundaries.append((idx, amp, abs(rel_pos - 0.5)))
            
            if reasonable_boundaries:
                # 结合位置合理性和幅度变化选择最佳零点
                reasonable_boundaries.sort(key=lambda x: x[2])  # 按照与中点距离排序
                boundary_idx = reasonable_boundaries[0][0]
                confidence = min(0.9, 0.6 + reasonable_boundaries[0][1] / max(1e-6, np.max(abs(smooth_data))))
            else:
                # 默认选择幅度最大的零点
                boundary_idx = zero_crossings[0][0]
                confidence = 0.7
        else:
            # 只有一个零点
            boundary_idx = zero_crossings[0][0]
            confidence = 0.8
        
        # 4. 优化边界，确保边界就是加速度开始变为负值的点
        # 向前查找最早的连续负值点
        for i in range(boundary_idx, max(0, boundary_idx - window_size), -1):
            if smooth_data[i] > 0:
                boundary_idx = i + 1
                break
                
        # 向后检查确保确实是负值区域开始
        negative_confirmed = False
        for i in range(boundary_idx, min(len(smooth_data), boundary_idx + window_size)):
            if smooth_data[i] < -0.05 * np.max(abs(smooth_data)):  # 确认有明显负值
                negative_confirmed = True
                break
                
        if not negative_confirmed:
            # 可能是噪声引起的过零点，降低可信度
            confidence *= 0.7
            
        return boundary_idx, confidence

    def detect_motion_by_integration(self, acc_data, only_acceleration_phase=True, detection_method='velocity'):
        """
        通过积分方法检测运动，支持速度和位移两种检测方式

        参数:
            acc_data: 三轴加速度数据 [N, 3]
            only_acceleration_phase: 是否只检测加速阶段
            detection_method: 检测方法 ('velocity' 或 'displacement')
                            'velocity': 使用速度最大值点检测加速部分
                            'displacement': 使用位移最大值点检测加速部分

        返回:
            start_idx: 事件开始样本索引
            end_idx: 事件结束样本索引
            confidence: 检测置信度(0-1)
            main_axis: 主运动轴(0=X, 1=Y, 2=Z)
        """
        # 1. 预处理数据 - 去除重力和噪声
        processed_data = self.preprocess_data(acc_data)

        # 2. 计算X轴和Z轴的方差，确定主运动轴
        x_var = np.var(processed_data[:, 0])
        z_var = np.var(processed_data[:, 2])

        main_axis = 0 if x_var > z_var else 2  # 方差大的轴为主运动轴
        main_axis_name = 'X' if main_axis == 0 else 'Z'
        print(f"检测到主运动轴: {main_axis_name}轴 (方差: {x_var:.4f} vs {z_var:.4f})")
        print(f"使用检测方法: {detection_method}")

        # 3. 对三轴数据进行平滑处理
        window = 5
        smoothed_data_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
        smoothed_data_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
        smoothed_data_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')

        # 计算三轴合成加速度幅值用于起点检测
        combined_acceleration = np.sqrt(smoothed_data_x**2 + smoothed_data_y**2 + smoothed_data_z**2)

        # 4. 找到信号开始点 - 合成加速度首次超过阈值的点
        threshold = 0.1 * np.max(combined_acceleration)
        start_candidates = np.where(combined_acceleration > threshold)[0]

        if len(start_candidates) == 0:
            print("警告: 未检测到明显的运动起点")
            return 0, len(acc_data)-1, 0.5, main_axis

        start_idx = start_candidates[0]

        # 5. 积分计算三轴速度和合速度矢量
        dt = 1.0 / self.sample_rate

        # 分别计算三轴速度
        velocity_x = np.cumsum(smoothed_data_x[start_idx:]) * dt
        velocity_y = np.cumsum(smoothed_data_y[start_idx:]) * dt
        velocity_z = np.cumsum(smoothed_data_z[start_idx:]) * dt

        # 计算合速度矢量的大小
        velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)

        # 为了兼容性，也保留主轴速度（用于位移方法）
        main_axis_data = processed_data[:, main_axis]
        smoothed_main_axis = np.convolve(main_axis_data, np.ones(window)/window, mode='same')
        velocity_main_axis = np.cumsum(smoothed_main_axis[start_idx:]) * dt
        displacement = np.cumsum(velocity_main_axis) * dt

        print(f"计算三轴合速度矢量，起点索引: {start_idx}, 时间: {start_idx/self.sample_rate:.3f}s")

        # 6. 根据检测方法确定加速阶段结束点
        if only_acceleration_phase:
            if detection_method == 'velocity':
                # 方法1: 使用三轴合速度矢量最大值点检测加速部分（带最小时间阈值）
                end_idx = self._find_acceleration_end_by_velocity_magnitude(velocity_magnitude, start_idx, min_duration_sec=0.2)
                print(f"使用三轴合速度矢量最大值方法检测到加速阶段结束点")
            elif detection_method == 'velocity_individual':
                # 方法2: 三轴独立检测方式 - 分别检测三轴，选取最早时间
                end_idx = self._find_acceleration_end_by_individual_axes(velocity_x, velocity_y, velocity_z, start_idx, min_duration_sec=0.2)
                print(f"使用三轴独立检测方法检测到加速阶段结束点")
            elif detection_method == 'velocity_main_secondary':
                # 方法3: 主轴+副轴合成方式 - 主轴单独，副轴合成，选取较早时间
                end_idx = self._find_acceleration_end_by_main_secondary_axes(velocity_x, velocity_y, velocity_z, main_axis, start_idx, min_duration_sec=0.2)
                print(f"使用主轴+副轴合成检测方法检测到加速阶段结束点")
            elif detection_method == 'displacement':
                # 方法4: 使用位移最大值点检测加速部分（带最小时间阈值）
                end_idx = self._find_acceleration_end_by_displacement(displacement, start_idx, min_duration_sec=0.2)
                print(f"使用位移最大值方法检测到加速阶段结束点")
            else:
                raise ValueError("detection_method 必须是 'velocity', 'velocity_individual', 'velocity_main_secondary' 或 'displacement'")
        else:
            # 检测完整运动事件 - 找到速度接近0的点
            end_idx = self._find_motion_end_by_velocity_zero(velocity_magnitude, start_idx)

        # 确保索引在有效范围内
        end_idx = min(len(acc_data) - 1, max(start_idx + 5, end_idx))

        # 7. 计算置信度 - 基于信号强度和方差比
        signal_strength = np.max(combined_acceleration[start_idx:end_idx+1])
        variance_ratio = max(x_var, z_var) / (min(x_var, z_var) + 1e-6)

        # 归一化置信度到0-1
        confidence = min(1.0, 0.5 + 0.25 * (signal_strength / threshold - 1) + 0.25 * min(1.0, np.log10(variance_ratio)))

        return start_idx, end_idx, confidence, main_axis

    def _find_acceleration_end_by_velocity_magnitude(self, velocity_magnitude, start_idx, min_duration_sec=0.2):
        """
        通过三轴合速度矢量最大值点检测加速阶段结束点
        从零时刻开始计算，找到第一次减小之前的最大速度点

        参数:
            velocity_magnitude: 三轴合速度矢量幅值数组（从start_idx开始的相对速度）
            start_idx: 运动开始索引
            min_duration_sec: 最小加速持续时间（秒），默认0.2秒

        返回:
            end_idx: 加速阶段结束索引
        """
        if len(velocity_magnitude) < 5:
            return start_idx + len(velocity_magnitude) - 1

        # 计算最小持续时间对应的样本数
        min_samples = int(min_duration_sec * self.sample_rate)
        print(f"三轴合速度最小加速持续时间: {min_duration_sec}秒 ({min_samples}个样本)")
        print(f"寻找从零时刻开始第一次减小之前的最大速度点")

        # 1. 找到速度第一次开始减小的点
        first_decrease_point = self._find_first_velocity_decrease(velocity_magnitude, min_samples)

        if first_decrease_point is not None:
            print(f"找到速度第一次开始减小的点: 索引{first_decrease_point}, 时间{first_decrease_point/self.sample_rate:.3f}s")
            # 在减小点之前寻找最大值
            search_range = velocity_magnitude[:first_decrease_point + 1]
        else:
            print("未找到明显的速度减小点，使用全部数据")
            search_range = velocity_magnitude

        # 2. 在搜索范围内找到最大速度点
        if len(search_range) <= min_samples:
            # 搜索范围太小，使用最小时间阈值
            end_idx = start_idx + min_samples
            print(f"搜索范围太小，使用最小时间阈值: 索引{min_samples}")
            return end_idx

        # 在时间阈值之后寻找最大值
        valid_search_range = search_range[min_samples:]
        if len(valid_search_range) == 0:
            end_idx = start_idx + min_samples
            print(f"有效搜索范围为空，使用最小时间阈值: 索引{min_samples}")
            return end_idx

        # 找到最大速度点
        max_vel_idx_in_valid = np.argmax(valid_search_range)
        max_vel_idx = min_samples + max_vel_idx_in_valid
        max_vel_value = velocity_magnitude[max_vel_idx]

        # 3. 验证最大值点的有效性
        max_velocity_magnitude = np.max(velocity_magnitude)

        # 检查是否足够显著
        if max_vel_value < 0.2 * max_velocity_magnitude:
            print(f"最大速度点不够显著 ({max_vel_value:.3f} < {0.2 * max_velocity_magnitude:.3f})，使用备用策略")
            end_idx = self._fallback_velocity_magnitude_detection(velocity_magnitude, start_idx, min_samples, max_velocity_magnitude)
        else:
            end_idx = start_idx + max_vel_idx
            duration = max_vel_idx / self.sample_rate
            print(f"找到第一次减小前的最大速度点: 索引{max_vel_idx}, 持续时间{duration:.3f}s, 合速度{max_vel_value:.3f}")

        return end_idx

    def _find_first_velocity_decrease(self, velocity_magnitude, min_samples):
        """
        找到速度第一次开始减小的点

        参数:
            velocity_magnitude: 速度幅值数组
            min_samples: 最小样本数，减小点必须在此之后

        返回:
            decrease_point_idx: 减小点索引，如果没有找到则返回None
        """
        if len(velocity_magnitude) <= min_samples + 5:
            return None

        # 使用滑动窗口检测速度减小
        window_size = max(3, self.sample_rate // 50)  # 约0.02秒的窗口

        # 从最小时间阈值之后开始检测
        for i in range(min_samples, len(velocity_magnitude) - window_size):
            # 计算当前点和后续窗口的平均速度
            current_velocity = velocity_magnitude[i]
            future_velocity = np.mean(velocity_magnitude[i+1:i+1+window_size])

            # 检查是否开始减小
            if future_velocity < current_velocity * 0.95:  # 减小幅度超过5%
                # 进一步验证这是一个稳定的减小趋势
                if self._verify_velocity_decrease(velocity_magnitude, i, window_size):
                    print(f"检测到速度开始减小: 索引{i}, 时间{i/self.sample_rate:.3f}s")
                    print(f"  当前速度: {current_velocity:.3f}, 后续速度: {future_velocity:.3f}")
                    return i

        print("未找到明显的速度减小趋势")
        return None

    def _verify_velocity_decrease(self, velocity_magnitude, decrease_point, window_size):
        """
        验证速度减小是否稳定

        参数:
            velocity_magnitude: 速度幅值数组
            decrease_point: 疑似减小点的索引
            window_size: 验证窗口大小

        返回:
            is_stable: 是否是稳定的减小趋势
        """
        # 检查减小点之后的一段时间内速度是否持续减小或保持较低水平
        verify_window = min(window_size * 3, len(velocity_magnitude) - decrease_point)

        if verify_window < window_size:
            return False

        # 计算减小点前后的速度变化
        pre_velocity = velocity_magnitude[decrease_point]
        post_velocities = velocity_magnitude[decrease_point+1:decrease_point+1+verify_window]

        # 统计后续速度低于减小点速度的比例
        lower_count = np.sum(post_velocities < pre_velocity * 0.98)  # 低于98%
        consistency_ratio = lower_count / len(post_velocities)

        # 如果后续速度大部分时间都低于减小点速度，认为是稳定的减小
        is_stable = consistency_ratio > 0.6

        print(f"  减小趋势验证: 后续低速比例 {consistency_ratio:.2f} ({'稳定' if is_stable else '不稳定'})")

        return is_stable

    def _find_acceleration_end_by_individual_axes(self, velocity_x, velocity_y, velocity_z, start_idx, min_duration_sec=0.15):
        """
        三轴独立检测方式：分别检测X、Y、Z三轴的最大速度时间，选取最早的时间

        参数:
            velocity_x, velocity_y, velocity_z: 三轴速度数组
            start_idx: 运动开始索引
            min_duration_sec: 最小加速持续时间（秒）

        返回:
            end_idx: 加速阶段结束索引（三轴中最早的）
        """
        if len(velocity_x) < 5 or len(velocity_y) < 5 or len(velocity_z) < 5:
            return start_idx + min(len(velocity_x), len(velocity_y), len(velocity_z)) - 1

        min_samples = int(min_duration_sec * self.sample_rate)
        print(f"三轴独立检测最小加速持续时间: {min_duration_sec}秒 ({min_samples}个样本)")
        print(f"分别检测X、Y、Z三轴，选取最早的最大速度时间")

        # 分别检测三轴的最大速度时间
        axis_names = ['X', 'Y', 'Z']
        axis_velocities = [velocity_x, velocity_y, velocity_z]
        axis_end_times = []

        for i, (axis_name, velocity_axis) in enumerate(zip(axis_names, axis_velocities)):
            print(f"\n检测{axis_name}轴:")

            # 找到该轴速度第一次减小的点
            first_decrease_point = self._find_first_velocity_decrease_single_axis(velocity_axis, min_samples, axis_name)

            if first_decrease_point is not None:
                search_range = velocity_axis[:first_decrease_point + 1]
                print(f"  {axis_name}轴减小点: 索引{first_decrease_point}, 时间{first_decrease_point/self.sample_rate:.3f}s")
            else:
                search_range = velocity_axis
                print(f"  {axis_name}轴未找到减小点，使用全部数据")

            # 在搜索范围内找最大速度点
            if len(search_range) > min_samples:
                valid_range = search_range[min_samples:]
                if len(valid_range) > 0:
                    max_idx_in_valid = np.argmax(np.abs(valid_range))  # 使用绝对值找最大速度
                    max_idx = min_samples + max_idx_in_valid
                    max_value = velocity_axis[max_idx]

                    # 检查显著性
                    max_abs_velocity = np.max(np.abs(velocity_axis))
                    if abs(max_value) >= 0.1 * max_abs_velocity:  # 至少10%的最大速度
                        axis_end_times.append((max_idx, abs(max_value), axis_name))
                        print(f"  {axis_name}轴最大速度点: 索引{max_idx}, 时间{max_idx/self.sample_rate:.3f}s, 速度{max_value:.3f}")
                    else:
                        print(f"  {axis_name}轴最大速度不够显著，跳过")
                else:
                    print(f"  {axis_name}轴有效范围为空，跳过")
            else:
                print(f"  {axis_name}轴搜索范围太小，跳过")

        # 选择最早的时间
        if axis_end_times:
            # 按时间排序，选择最早的
            axis_end_times.sort(key=lambda x: x[0])
            earliest_idx, earliest_value, earliest_axis = axis_end_times[0]
            end_idx = start_idx + earliest_idx
            duration = earliest_idx / self.sample_rate

            print(f"\n选择最早的轴: {earliest_axis}轴")
            print(f"最早时间: 索引{earliest_idx}, 持续时间{duration:.3f}s, 速度{earliest_value:.3f}")

            # 显示所有轴的检测结果
            print(f"所有轴的检测结果:")
            for idx, value, axis_name in axis_end_times:
                print(f"  {axis_name}轴: {idx/self.sample_rate:.3f}s (速度{value:.3f})")
        else:
            # 没有找到有效的轴，使用最小时间阈值
            end_idx = start_idx + min_samples
            print(f"所有轴都无效，使用最小时间阈值: 索引{min_samples}")

        return end_idx

    def _find_acceleration_end_by_main_secondary_axes(self, velocity_x, velocity_y, velocity_z, main_axis, start_idx, min_duration_sec=0.2):
        """
        主轴+副轴合成方式：主运动轴单独检测，其余两轴合成后检测，选取较早的时间

        参数:
            velocity_x, velocity_y, velocity_z: 三轴速度数组
            main_axis: 主运动轴索引 (0=X, 2=Z)
            start_idx: 运动开始索引
            min_duration_sec: 最小加速持续时间（秒）

        返回:
            end_idx: 加速阶段结束索引（主轴和副轴中较早的）
        """
        if len(velocity_x) < 5 or len(velocity_y) < 5 or len(velocity_z) < 5:
            return start_idx + min(len(velocity_x), len(velocity_y), len(velocity_z)) - 1

        min_samples = int(min_duration_sec * self.sample_rate)
        print(f"主轴+副轴合成检测最小加速持续时间: {min_duration_sec}秒 ({min_samples}个样本)")

        # 确定主轴和副轴
        axis_velocities = [velocity_x, velocity_y, velocity_z]
        axis_names = ['X', 'Y', 'Z']

        main_velocity = axis_velocities[main_axis]
        main_name = axis_names[main_axis]

        # 副轴：除主轴外的其他两轴
        secondary_indices = [i for i in range(3) if i != main_axis]
        secondary_velocities = [axis_velocities[i] for i in secondary_indices]
        secondary_names = [axis_names[i] for i in secondary_indices]

        print(f"主运动轴: {main_name}轴")
        print(f"副轴: {secondary_names[0]}轴 + {secondary_names[1]}轴")

        # 1. 检测主轴的最大速度时间
        print(f"\n检测主轴({main_name}轴):")
        main_decrease_point = self._find_first_velocity_decrease_single_axis(main_velocity, min_samples, main_name)

        if main_decrease_point is not None:
            main_search_range = main_velocity[:main_decrease_point + 1]
            print(f"  主轴减小点: 索引{main_decrease_point}, 时间{main_decrease_point/self.sample_rate:.3f}s")
        else:
            main_search_range = main_velocity
            print(f"  主轴未找到减小点，使用全部数据")

        main_end_idx = None
        if len(main_search_range) > min_samples:
            valid_range = main_search_range[min_samples:]
            if len(valid_range) > 0:
                max_idx_in_valid = np.argmax(np.abs(valid_range))
                max_idx = min_samples + max_idx_in_valid
                max_value = main_velocity[max_idx]

                # 检查显著性
                max_abs_velocity = np.max(np.abs(main_velocity))
                if abs(max_value) >= 0.1 * max_abs_velocity:
                    main_end_idx = max_idx
                    print(f"  主轴最大速度点: 索引{max_idx}, 时间{max_idx/self.sample_rate:.3f}s, 速度{max_value:.3f}")
                else:
                    print(f"  主轴最大速度不够显著")

        # 2. 检测副轴合成速度的最大速度时间
        print(f"\n检测副轴合成({secondary_names[0]}+{secondary_names[1]}):")

        # 计算副轴合成速度
        secondary_magnitude = np.sqrt(secondary_velocities[0]**2 + secondary_velocities[1]**2)

        secondary_decrease_point = self._find_first_velocity_decrease_single_axis(secondary_magnitude, min_samples, f"{secondary_names[0]}+{secondary_names[1]}")

        if secondary_decrease_point is not None:
            secondary_search_range = secondary_magnitude[:secondary_decrease_point + 1]
            print(f"  副轴合成减小点: 索引{secondary_decrease_point}, 时间{secondary_decrease_point/self.sample_rate:.3f}s")
        else:
            secondary_search_range = secondary_magnitude
            print(f"  副轴合成未找到减小点，使用全部数据")

        secondary_end_idx = None
        if len(secondary_search_range) > min_samples:
            valid_range = secondary_search_range[min_samples:]
            if len(valid_range) > 0:
                max_idx_in_valid = np.argmax(valid_range)  # 副轴合成总是非负
                max_idx = min_samples + max_idx_in_valid
                max_value = secondary_magnitude[max_idx]

                # 检查显著性
                max_magnitude = np.max(secondary_magnitude)
                if max_value >= 0.1 * max_magnitude:
                    secondary_end_idx = max_idx
                    print(f"  副轴合成最大速度点: 索引{max_idx}, 时间{max_idx/self.sample_rate:.3f}s, 速度{max_value:.3f}")
                else:
                    print(f"  副轴合成最大速度不够显著")

        # 3. 选择较早的时间
        valid_end_times = []
        if main_end_idx is not None:
            valid_end_times.append((main_end_idx, f"主轴({main_name})"))
        if secondary_end_idx is not None:
            valid_end_times.append((secondary_end_idx, f"副轴({secondary_names[0]}+{secondary_names[1]})"))

        if valid_end_times:
            # 选择较早的时间
            valid_end_times.sort(key=lambda x: x[0])
            earliest_idx, earliest_source = valid_end_times[0]
            end_idx = start_idx + earliest_idx
            duration = earliest_idx / self.sample_rate

            print(f"\n选择较早的时间: {earliest_source}")
            print(f"最早时间: 索引{earliest_idx}, 持续时间{duration:.3f}s")

            # 显示所有的检测结果
            print(f"所有检测结果:")
            for idx, source in valid_end_times:
                print(f"  {source}: {idx/self.sample_rate:.3f}s")
        else:
            # 没有找到有效的结果，使用最小时间阈值
            end_idx = start_idx + min_samples
            print(f"主轴和副轴都无效，使用最小时间阈值: 索引{min_samples}")

        return end_idx

    def _find_first_velocity_decrease_single_axis(self, velocity_axis, min_samples, axis_name):
        """
        为单个轴找到速度第一次开始减小的点

        参数:
            velocity_axis: 单轴速度数组
            min_samples: 最小样本数
            axis_name: 轴名称（用于调试输出）

        返回:
            decrease_point_idx: 减小点索引，如果没有找到则返回None
        """
        if len(velocity_axis) <= min_samples + 5:
            return None

        # 使用滑动窗口检测速度减小
        window_size = max(3, self.sample_rate // 50)  # 约0.02秒的窗口

        # 从最小时间阈值之后开始检测
        for i in range(min_samples, len(velocity_axis) - window_size):
            # 计算当前点和后续窗口的平均速度（使用绝对值）
            current_velocity = abs(velocity_axis[i])
            future_velocity = np.mean(np.abs(velocity_axis[i+1:i+1+window_size]))

            # 检查是否开始减小
            if future_velocity < current_velocity * 0.95:  # 减小超过5%
                # 进一步验证这是一个稳定的减小趋势
                if self._verify_velocity_decrease_single_axis(velocity_axis, i, window_size):
                    return i

        return None

    def _verify_velocity_decrease_single_axis(self, velocity_axis, decrease_point, window_size):
        """
        验证单轴速度减小是否稳定

        参数:
            velocity_axis: 单轴速度数组
            decrease_point: 疑似减小点的索引
            window_size: 验证窗口大小

        返回:
            is_stable: 是否是稳定的减小趋势
        """
        # 检查减小点之后的一段时间内速度是否持续减小或保持较低水平
        verify_window = min(window_size * 3, len(velocity_axis) - decrease_point)

        if verify_window < window_size:
            return False

        # 计算减小点前后的速度变化（使用绝对值）
        pre_velocity = abs(velocity_axis[decrease_point])
        post_velocities = np.abs(velocity_axis[decrease_point+1:decrease_point+1+verify_window])

        # 统计后续速度低于减小点速度的比例
        lower_count = np.sum(post_velocities < pre_velocity * 0.98)  # 低于98%
        consistency_ratio = lower_count / len(post_velocities)

        # 如果后续速度大部分时间都低于减小点速度，认为是稳定的减小
        return consistency_ratio > 0.6

    def _fallback_velocity_magnitude_detection(self, velocity_magnitude, start_idx, min_samples, max_velocity_magnitude):
        """
        备用合速度检测策略，当主要方法失败时使用

        参数:
            velocity_magnitude: 合速度矢量幅值数组
            start_idx: 起始索引
            min_samples: 最小样本数
            max_velocity_magnitude: 最大合速度幅值

        返回:
            end_idx: 结束索引
        """
        print("执行合速度备用检测策略...")

        # 策略1: 寻找满足时间阈值的最大合速度点
        # 只在最小时间之后寻找
        if len(velocity_magnitude) > min_samples:
            valid_velocity = velocity_magnitude[min_samples:]
            max_idx_in_valid = np.argmax(valid_velocity)
            max_idx = min_samples + max_idx_in_valid
            max_value = velocity_magnitude[max_idx]

            # 检查这个点是否足够显著
            if max_value >= 0.2 * max_velocity_magnitude:
                end_idx = start_idx + max_idx
                print(f"备用策略1成功: 时间阈值后的最大合速度点, 索引{max_idx}, 合速度{velocity_magnitude[max_idx]:.3f}")
                return end_idx

        # 策略2: 使用合速度阈值方法
        threshold = 0.6 * max_velocity_magnitude
        threshold_indices = np.where(velocity_magnitude >= threshold)[0]

        # 过滤掉时间太早的点
        valid_threshold_indices = threshold_indices[threshold_indices >= min_samples]

        if len(valid_threshold_indices) > 0:
            end_idx = start_idx + valid_threshold_indices[0]
            print(f"备用策略2成功: 60%合速度阈值方法, 索引{valid_threshold_indices[0]}")
            return end_idx

        # 策略3: 使用最小时间阈值作为结束点
        end_idx = start_idx + min_samples
        print(f"备用策略3: 使用最小时间阈值, 索引{min_samples}")
        return end_idx

    def _find_acceleration_end_by_velocity(self, velocity, start_idx, min_duration_sec=0.15):
        """
        通过速度最大值点检测加速阶段结束点
        避免减速阶段的反向速度最大值，并确保最小持续时间

        参数:
            velocity: 速度数组（从start_idx开始的相对速度）
            start_idx: 运动开始索引
            min_duration_sec: 最小加速持续时间（秒），默认0.2秒

        返回:
            end_idx: 加速阶段结束索引
        """
        if len(velocity) < 5:
            return start_idx + len(velocity) - 1

        # 计算最小持续时间对应的样本数
        min_samples = int(min_duration_sec * self.sample_rate)
        print(f"最小加速持续时间: {min_duration_sec}秒 ({min_samples}个样本)")

        # 1. 找到所有局部极值点（峰值和谷值）
        from scipy.signal import find_peaks

        # 使用更严格的峰值检测参数
        min_peak_distance = max(5, min_samples // 4)  # 峰值间最小距离

        # 找到正向峰值（速度最大值）
        pos_peaks, pos_properties = find_peaks(velocity, height=0, distance=min_peak_distance, prominence=0.01)
        # 找到负向峰值（速度最小值，即反向最大值）
        neg_peaks, neg_properties = find_peaks(-velocity, height=0, distance=min_peak_distance, prominence=0.01)

        # 2. 确定主要运动方向（初始加速方向）
        # 查看前10%的数据来确定初始运动方向
        initial_samples = max(5, len(velocity) // 10)
        initial_velocity_trend = np.mean(velocity[:initial_samples])

        if initial_velocity_trend >= 0:
            # 正向加速，寻找第一个正向速度峰值
            main_direction = 1
            target_peaks = pos_peaks
            target_properties = pos_properties
            print(f"检测到正向加速运动，寻找正向速度峰值")
        else:
            # 负向加速，寻找第一个负向速度峰值
            main_direction = -1
            target_peaks = neg_peaks
            target_properties = neg_properties
            print(f"检测到负向加速运动，寻找负向速度峰值")

        # 3. 应用多重过滤条件选择合适的峰值
        valid_peaks = []
        max_abs_velocity = np.max(np.abs(velocity))

        for i, peak_idx in enumerate(target_peaks):
            peak_value = abs(velocity[peak_idx])

            # 条件1: 时间阈值 - 峰值必须在最小持续时间之后
            if peak_idx < min_samples:
                print(f"  峰值{peak_idx}被过滤: 时间太早 ({peak_idx/self.sample_rate:.3f}s < {min_duration_sec}s)")
                continue

            # 条件2: 显著性阈值 - 峰值必须足够显著
            if peak_value < 0.3 * max_abs_velocity:
                print(f"  峰值{peak_idx}被过滤: 幅度太小 ({peak_value:.3f} < {0.3 * max_abs_velocity:.3f})")
                continue

            # 条件3: 趋势一致性 - 确保是真正的加速峰值
            # 检查峰值前的趋势是否为加速
            trend_window = min(peak_idx, min_samples // 2)
            if trend_window > 3:
                trend_start = max(0, peak_idx - trend_window)
                trend_slope = (velocity[peak_idx] - velocity[trend_start]) / trend_window
                expected_slope_sign = main_direction

                if np.sign(trend_slope) != expected_slope_sign:
                    print(f"  峰值{peak_idx}被过滤: 趋势不一致 (斜率符号: {np.sign(trend_slope)} != {expected_slope_sign})")
                    continue

            # 条件4: 峰值质量 - 使用prominence（突出度）评估
            if 'prominences' in target_properties:
                prominence = target_properties['prominences'][i]
                min_prominence = 0.1 * max_abs_velocity
                if prominence < min_prominence:
                    print(f"  峰值{peak_idx}被过滤: 突出度不足 ({prominence:.3f} < {min_prominence:.3f})")
                    continue

            valid_peaks.append((peak_idx, peak_value))
            print(f"  有效峰值: 索引{peak_idx}, 时间{peak_idx/self.sample_rate:.3f}s, 速度{velocity[peak_idx]:.3f}")

        # 4. 选择最佳峰值
        if valid_peaks:
            # 选择第一个有效峰值（时间最早的）
            best_peak_idx, best_peak_value = valid_peaks[0]
            end_idx = start_idx + best_peak_idx
            duration = best_peak_idx / self.sample_rate
            print(f"选择最佳峰值: 索引{best_peak_idx}, 持续时间{duration:.3f}s, 速度{velocity[best_peak_idx]:.3f}")

        else:
            # 没有找到有效峰值，使用备用策略
            print("未找到有效峰值，使用备用策略")
            end_idx = self._fallback_velocity_detection(velocity, start_idx, min_samples, main_direction, max_abs_velocity)

        return end_idx

    def _fallback_velocity_detection(self, velocity, start_idx, min_samples, main_direction, max_abs_velocity):
        """
        备用速度检测策略，当主要方法失败时使用

        参数:
            velocity: 速度数组
            start_idx: 起始索引
            min_samples: 最小样本数
            main_direction: 主要运动方向
            max_abs_velocity: 最大绝对速度

        返回:
            end_idx: 结束索引
        """
        print("执行备用检测策略...")

        # 策略1: 寻找满足时间阈值的最大速度点
        # 只在最小时间之后寻找
        if len(velocity) > min_samples:
            valid_velocity = velocity[min_samples:]
            if main_direction > 0:
                max_idx_in_valid = np.argmax(valid_velocity)
            else:
                max_idx_in_valid = np.argmin(valid_velocity)

            max_idx = min_samples + max_idx_in_valid
            max_value = abs(velocity[max_idx])

            # 检查这个点是否足够显著
            if max_value >= 0.2 * max_abs_velocity:
                end_idx = start_idx + max_idx
                print(f"备用策略1成功: 时间阈值后的最大速度点, 索引{max_idx}, 速度{velocity[max_idx]:.3f}")
                return end_idx

        # 策略2: 使用速度阈值方法
        threshold = 0.6 * max_abs_velocity * main_direction
        threshold_indices = np.where(velocity * main_direction >= threshold)[0]

        # 过滤掉时间太早的点
        valid_threshold_indices = threshold_indices[threshold_indices >= min_samples]

        if len(valid_threshold_indices) > 0:
            end_idx = start_idx + valid_threshold_indices[0]
            print(f"备用策略2成功: 60%阈值方法, 索引{valid_threshold_indices[0]}")
            return end_idx

        # 策略3: 使用最小时间阈值作为结束点
        end_idx = start_idx + min_samples
        print(f"备用策略3: 使用最小时间阈值, 索引{min_samples}")
        return end_idx

    def _find_acceleration_end_by_displacement(self, displacement, start_idx, min_duration_sec=0.2):
        """
        通过位移最大值点检测加速阶段结束点，基于速度转折点分析
        先找到速度转折点，然后在转折点之前找到正向运动的位移最大点

        参数:
            displacement: 位移数组（从start_idx开始的相对位移）
            start_idx: 运动开始索引
            min_duration_sec: 最小加速持续时间（秒），默认0.2秒

        返回:
            end_idx: 加速阶段结束索引
        """
        if len(displacement) < 5:
            return start_idx + len(displacement) - 1

        # 计算最小持续时间对应的样本数
        min_samples = int(min_duration_sec * self.sample_rate)
        print(f"位移方法最小加速持续时间: {min_duration_sec}秒 ({min_samples}个样本)")

        # 1. 计算速度（位移的一阶导数）
        dt = 1.0 / self.sample_rate
        velocity = np.gradient(displacement) / dt

        # 2. 找到速度转折点（速度方向改变的点）
        velocity_turning_point = self._find_velocity_turning_point(velocity, min_samples)

        if velocity_turning_point is not None:
            print(f"找到速度转折点: 索引{velocity_turning_point}, 时间{velocity_turning_point/self.sample_rate:.3f}s")
            # 在转折点之前寻找位移最大点
            search_end = velocity_turning_point
        else:
            print("未找到明显的速度转折点，使用全部数据")
            search_end = len(displacement)

        # 3. 确定主要运动方向（基于转折点前的位移趋势）
        if search_end > min_samples:
            displacement_trend = displacement[search_end-1] - displacement[min_samples]
        else:
            displacement_trend = displacement[-1] if len(displacement) > 0 else 0

        if abs(displacement_trend) < 1e-6:
            # 位移变化太小，使用最小时间阈值
            return start_idx + max(min_samples, len(displacement) // 2)

        main_direction = 1 if displacement_trend > 0 else -1
        print(f"检测到{'正向' if main_direction > 0 else '负向'}位移运动趋势")

        # 4. 在转折点之前寻找位移极值点
        search_displacement = displacement[:search_end]

        if len(search_displacement) <= min_samples:
            # 搜索范围太小，使用最小时间阈值
            end_idx = start_idx + min_samples
            print(f"搜索范围太小，使用最小时间阈值: 索引{min_samples}")
            return end_idx

        # 5. 寻找位移的极值点（在转折点之前）
        from scipy.signal import find_peaks

        # 使用更严格的峰值检测参数
        min_peak_distance = max(5, min_samples // 4)

        if main_direction > 0:
            # 正向运动，寻找位移最大值
            peaks, _ = find_peaks(search_displacement, distance=min_peak_distance)

            # 过滤掉时间太早的峰值
            valid_peaks = peaks[peaks >= min_samples]

            if len(valid_peaks) > 0:
                max_peak_idx = valid_peaks[np.argmax(search_displacement[valid_peaks])]
                end_idx = start_idx + max_peak_idx
                duration = max_peak_idx / self.sample_rate
                print(f"找到转折点前的正向位移最大值点: 索引{max_peak_idx}, 持续时间{duration:.3f}s, 位移值{search_displacement[max_peak_idx]:.3f}")
            else:
                # 没有有效峰值，在时间阈值后寻找局部最大值
                if len(search_displacement) > min_samples:
                    valid_displacement = search_displacement[min_samples:]
                    max_disp_idx = min_samples + np.argmax(valid_displacement)
                else:
                    max_disp_idx = len(search_displacement) - 1
                end_idx = start_idx + max_disp_idx
                duration = max_disp_idx / self.sample_rate
                print(f"使用转折点前的局部位移最大值点: 索引{max_disp_idx}, 持续时间{duration:.3f}s")
        else:
            # 负向运动，寻找位移最小值（绝对值最大）
            peaks, _ = find_peaks(-search_displacement, distance=min_peak_distance)

            # 过滤掉时间太早的峰值
            valid_peaks = peaks[peaks >= min_samples]

            if len(valid_peaks) > 0:
                min_peak_idx = valid_peaks[np.argmax(-search_displacement[valid_peaks])]
                end_idx = start_idx + min_peak_idx
                duration = min_peak_idx / self.sample_rate
                print(f"找到转折点前的负向位移最大值点: 索引{min_peak_idx}, 持续时间{duration:.3f}s, 位移值{search_displacement[min_peak_idx]:.3f}")
            else:
                # 没有有效峰值，在时间阈值后寻找局部最小值
                if len(search_displacement) > min_samples:
                    valid_displacement = search_displacement[min_samples:]
                    min_disp_idx = min_samples + np.argmin(valid_displacement)
                else:
                    min_disp_idx = len(search_displacement) - 1
                end_idx = start_idx + min_disp_idx
                duration = min_disp_idx / self.sample_rate
                print(f"使用转折点前的局部位移最小值点: 索引{min_disp_idx}, 持续时间{duration:.3f}s")

        return end_idx

    def _find_velocity_turning_point(self, velocity, min_samples):
        """
        找到速度的转折点（速度方向改变的点）

        参数:
            velocity: 速度数组
            min_samples: 最小样本数，转折点必须在此之后

        返回:
            turning_point_idx: 转折点索引，如果没有找到则返回None
        """
        if len(velocity) <= min_samples + 5:
            return None

        # 1. 确定初始运动方向
        # 使用前几个样本的平均值来确定初始方向
        initial_window = min(10, len(velocity) // 5)
        if initial_window < 3:
            return None

        initial_velocity = np.mean(velocity[min_samples:min_samples + initial_window])

        if abs(initial_velocity) < 1e-6:
            # 初始速度太小，无法确定方向
            return None

        initial_direction = 1 if initial_velocity > 0 else -1
        print(f"初始速度方向: {'正向' if initial_direction > 0 else '负向'} (速度: {initial_velocity:.3f})")

        # 2. 寻找速度方向改变的点
        # 使用滑动窗口检测方向变化
        window_size = max(5, self.sample_rate // 20)  # 约0.05秒的窗口

        for i in range(min_samples + window_size, len(velocity) - window_size):
            # 计算当前窗口的平均速度
            current_velocity = np.mean(velocity[i:i + window_size])
            current_direction = 1 if current_velocity > 0 else -1

            # 检查方向是否改变
            if current_direction != initial_direction:
                # 进一步验证这是一个稳定的方向改变
                if self._verify_direction_change(velocity, i, window_size, initial_direction):
                    print(f"检测到速度方向改变: 索引{i}, 时间{i/self.sample_rate:.3f}s")
                    print(f"  初始方向: {'正向' if initial_direction > 0 else '负向'}")
                    print(f"  新方向: {'正向' if current_direction > 0 else '负向'}")
                    return i

        # 3. 如果没有找到明显的方向改变，寻找速度接近零的点
        zero_threshold = 0.1 * np.max(np.abs(velocity))

        for i in range(min_samples, len(velocity)):
            if abs(velocity[i]) < zero_threshold:
                # 检查这个零点是否稳定
                if i + window_size < len(velocity):
                    next_window_velocity = np.mean(velocity[i:i + window_size])
                    if abs(next_window_velocity) < zero_threshold:
                        print(f"检测到速度接近零点: 索引{i}, 时间{i/self.sample_rate:.3f}s")
                        return i

        print("未找到明显的速度转折点")
        return None

    def _verify_direction_change(self, velocity, change_point, window_size, initial_direction):
        """
        验证速度方向改变是否稳定

        参数:
            velocity: 速度数组
            change_point: 疑似改变点的索引
            window_size: 验证窗口大小
            initial_direction: 初始方向

        返回:
            is_stable: 是否是稳定的方向改变
        """
        # 检查改变点之后的一段时间内方向是否保持稳定
        verify_window = min(window_size * 2, len(velocity) - change_point)

        if verify_window < window_size:
            return False

        # 计算改变点之后的速度方向
        post_change_velocity = velocity[change_point:change_point + verify_window]

        # 统计新方向的一致性
        new_direction_count = 0
        total_samples = len(post_change_velocity)

        for vel in post_change_velocity:
            if abs(vel) > 1e-6:  # 忽略接近零的值
                vel_direction = 1 if vel > 0 else -1
                if vel_direction != initial_direction:
                    new_direction_count += 1

        # 如果新方向占主导地位（>60%），认为是稳定的方向改变
        consistency_ratio = new_direction_count / total_samples
        is_stable = consistency_ratio > 0.6

        print(f"  方向改变验证: 新方向一致性 {consistency_ratio:.2f} ({'稳定' if is_stable else '不稳定'})")

        return is_stable

    def _find_motion_end_by_velocity_zero(self, velocity, start_idx):
        """
        通过速度接近零点检测完整运动事件结束点

        参数:
            velocity: 速度数组（从start_idx开始的相对速度）
            start_idx: 运动开始索引

        返回:
            end_idx: 运动结束索引
        """
        if len(velocity) < 5:
            return start_idx + len(velocity) - 1

        # 寻找速度首次接近0的点（在达到峰值之后）
        max_velocity = np.max(np.abs(velocity))
        zero_threshold = 0.05 * max_velocity

        # 先找到速度峰值
        peak_idx = np.argmax(np.abs(velocity))

        # 从峰值之后开始寻找接近零的点
        for i in range(peak_idx + 1, len(velocity)):
            if abs(velocity[i]) < zero_threshold:
                end_idx = start_idx + i
                print(f"找到速度接近零点: 索引{i}, 速度值{velocity[i]:.3f}")
                return end_idx

        # 如果没找到，返回数组末尾
        return start_idx + len(velocity) - 1

    def visualize_integration_detection(self, acc_data, start_idx, end_idx, main_axis, detection_method='velocity', save_path=None):
        """
        可视化积分法检测结果，支持速度和位移两种检测方法

        参数:
            acc_data: 加速度数据
            start_idx: 事件起始索引
            end_idx: 事件结束索引
            main_axis: 主运动轴索引(0=X, 1=Y, 2=Z)
            detection_method: 检测方法 ('velocity' 或 'displacement')
            save_path: 保存路径
        """
        # 预处理数据
        processed_data = self.preprocess_data(acc_data)

        # 创建包含5个子图的画布
        plt.figure(figsize=(15, 15))

        # 计算时间轴
        time = np.arange(len(acc_data)) / self.sample_rate

        # 1. 展示原始加速度数据 (子图1)
        plt.subplot(5, 1, 1)
        plt.plot(time, acc_data[:, 0], 'r-', label='X轴')
        plt.plot(time, acc_data[:, 1], 'g-', label='Y轴')
        plt.plot(time, acc_data[:, 2], 'b-', label='Z轴')
        plt.axvspan(start_idx/self.sample_rate, end_idx/self.sample_rate, color='y', alpha=0.3)
        plt.title('原始加速度数据')
        plt.ylabel('加速度 (m/s²)')
        plt.legend()
        plt.grid(True)

        # 2. 展示过滤后的加速度数据 (子图2)
        plt.subplot(5, 1, 2)
        plt.plot(time, processed_data[:, 0], 'r-', label='X轴')
        plt.plot(time, processed_data[:, 1], 'g-', label='Y轴')
        plt.plot(time, processed_data[:, 2], 'b-', label='Z轴')
        plt.axvspan(start_idx/self.sample_rate, end_idx/self.sample_rate, color='y', alpha=0.3)
        plt.title('过滤后的加速度数据')
        plt.ylabel('加速度 (m/s²)')
        plt.legend()
        plt.grid(True)

        # 3. 突出显示主运动轴 (子图3)
        plt.subplot(5, 1, 3)

        # 平滑主轴数据
        window = 5
        main_axis_data = processed_data[:, main_axis]
        smoothed_data = np.convolve(main_axis_data, np.ones(window)/window, mode='same')

        axis_name = 'X' if main_axis == 0 else 'Z'
        plt.plot(time, main_axis_data, f'{"r" if main_axis == 0 else "b"}-',
                label=f'{axis_name}轴 (原始)')
        plt.plot(time, smoothed_data, 'k-', label=f'{axis_name}轴 (平滑)')
        plt.axvspan(start_idx/self.sample_rate, end_idx/self.sample_rate, color='y', alpha=0.3)
        plt.title(f'主运动轴 ({axis_name}轴) - 方差: {np.var(main_axis_data):.4f}')
        plt.ylabel('加速度 (m/s²)')
        plt.legend()
        plt.grid(True)

        # 计算积分数据（在子图外计算，避免重复计算）
        dt = 1.0 / self.sample_rate
        velocity_segment = np.zeros(0)
        displacement_segment = np.zeros(0)
        velocity_magnitude_segment = np.zeros(0)

        if start_idx < len(smoothed_data):
            # 计算主轴速度和位移
            velocity_segment = np.cumsum(smoothed_data[start_idx:]) * dt
            displacement_segment = np.cumsum(velocity_segment) * dt

            # 计算三轴合速度矢量
            window = 5
            smoothed_data_x = np.convolve(processed_data[:, 0], np.ones(window)/window, mode='same')
            smoothed_data_y = np.convolve(processed_data[:, 1], np.ones(window)/window, mode='same')
            smoothed_data_z = np.convolve(processed_data[:, 2], np.ones(window)/window, mode='same')

            velocity_x = np.cumsum(smoothed_data_x[start_idx:]) * dt
            velocity_y = np.cumsum(smoothed_data_y[start_idx:]) * dt
            velocity_z = np.cumsum(smoothed_data_z[start_idx:]) * dt
            velocity_magnitude_segment = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)

        # 4. 展示积分结果 - 速度对比 (子图4)
        plt.subplot(5, 1, 4)

        if len(velocity_segment) > 0:
            velocity = np.zeros_like(time)
            velocity[start_idx:start_idx+len(velocity_segment)] = velocity_segment

            # 显示主轴速度
            plt.plot(time, velocity, 'g-', label=f'主轴({axis_name})速度', linewidth=2, alpha=0.7)

            # 显示三轴合速度矢量
            if len(velocity_magnitude_segment) > 0:
                velocity_magnitude = np.zeros_like(time)
                velocity_magnitude[start_idx:start_idx+len(velocity_magnitude_segment)] = velocity_magnitude_segment
                plt.plot(time, velocity_magnitude, 'purple', label='三轴合速度矢量', linewidth=3)

                # 标记合速度最大值点
                max_vel_mag_idx = np.argmax(velocity_magnitude_segment)
                max_vel_mag_time = (start_idx + max_vel_mag_idx) / self.sample_rate
                max_vel_mag_value = velocity_magnitude_segment[max_vel_mag_idx]
                plt.plot(max_vel_mag_time, max_vel_mag_value, 'o', color='purple', markersize=8, label='合速度最大值点')
                plt.annotate(f'合速度峰值\n({max_vel_mag_value:.3f} m/s)',
                           xy=(max_vel_mag_time, max_vel_mag_value),
                           xytext=(max_vel_mag_time + 0.1, max_vel_mag_value + 0.1),
                           arrowprops=dict(arrowstyle='->', color='purple'))

            plt.axvspan(start_idx/self.sample_rate, end_idx/self.sample_rate, color='y', alpha=0.3)
            plt.axhline(y=0, color='r', linestyle='--', alpha=0.7)

            # 标记主轴速度最大值点
            max_vel_idx = np.argmax(np.abs(velocity_segment))
            max_vel_time = (start_idx + max_vel_idx) / self.sample_rate
            max_vel_value = velocity_segment[max_vel_idx]
            plt.plot(max_vel_time, max_vel_value, 'go', markersize=6, label='主轴速度最大值点', alpha=0.7)

            # 如果使用速度方法，突出显示检测边界
            if detection_method == 'velocity':
                plt.axvline(x=end_idx/self.sample_rate, color='m', linestyle='-', linewidth=3,
                           label='速度方法检测边界')

            plt.title('积分结果 - 速度对比（主轴 vs 三轴合成）')
            plt.ylabel('速度 (m/s)')
            plt.legend()
            plt.grid(True)

        # 5. 展示积分结果 - 位移 (子图5)
        plt.subplot(5, 1, 5)

        if len(displacement_segment) > 0:
            displacement = np.zeros_like(time)
            displacement[start_idx:start_idx+len(displacement_segment)] = displacement_segment

            plt.plot(time, displacement, 'b-', label='二次积分位移', linewidth=2)
            plt.axvspan(start_idx/self.sample_rate, end_idx/self.sample_rate, color='y', alpha=0.3)
            plt.axhline(y=0, color='r', linestyle='--', alpha=0.7)

            # 标记位移最大值点
            if len(displacement_segment) > 0:
                if np.max(displacement_segment) > abs(np.min(displacement_segment)):
                    max_disp_idx = np.argmax(displacement_segment)
                    max_disp_value = displacement_segment[max_disp_idx]
                    label_text = '位移最大值点'
                else:
                    max_disp_idx = np.argmin(displacement_segment)
                    max_disp_value = displacement_segment[max_disp_idx]
                    label_text = '位移最小值点'

                max_disp_time = (start_idx + max_disp_idx) / self.sample_rate
                plt.plot(max_disp_time, max_disp_value, 'bo', markersize=8, label=label_text)
                plt.annotate(f'位移极值\n({max_disp_value:.3f} m)',
                           xy=(max_disp_time, max_disp_value),
                           xytext=(max_disp_time + 0.1, max_disp_value + 0.01),
                           arrowprops=dict(arrowstyle='->', color='blue'))

            # 如果使用位移方法，突出显示检测边界
            if detection_method == 'displacement':
                plt.axvline(x=end_idx/self.sample_rate, color='c', linestyle='-', linewidth=3,
                           label='位移方法检测边界')

            plt.title('积分结果 - 位移')
            plt.xlabel('时间 (秒)')
            plt.ylabel('位移 (m)')
            plt.legend()
            plt.grid(True)

        plt.tight_layout()

        # 保存或显示
        if save_path:
            plt.savefig(save_path, dpi=300)
            plt.close()
        else:
            plt.show()

def time_to_seconds(time_str):
    """将HH-MM-SS.mmm格式转换为秒数"""
    parts = time_str.split('.')
    time_parts = parts[0].split('-')
    hours = int(time_parts[0])
    minutes = int(time_parts[1])
    seconds = int(time_parts[2])
    microseconds = int(float('0.' + parts[1]) * 1000000) if len(parts) > 1 else 0
    return hours * 3600 + minutes * 60 + seconds + microseconds / 1000000

def convert_to_timestamp(base_time, sample_idx, sample_rate):
    """将样本索引转换为实际时间戳"""
    seconds = sample_idx / sample_rate
    base_seconds = time_to_seconds(base_time)
    total_seconds = base_seconds + seconds
    
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = total_seconds % 60
    return f"{hours:02d}-{minutes:02d}-{seconds:06.3f}"

def process_file(file_path, visualize=True, only_acceleration=True, detection_method='velocity_individual'):
    """
    处理单个加速度数据文件

    参数:
        file_path: 文件路径
        visualize: 是否进行可视化
        only_acceleration: 是否只检测加速阶段
        detection_method: 检测方法 ('velocity' 或 'displacement')
    """
    print(f"处理文件: {file_path}")
    print(f"{'仅检测加速阶段' if only_acceleration else '检测完整运动事件'}")
    print(f"检测方法: {detection_method}")

    try:
        # 读取数据
        df = pd.read_csv(file_path)
        acc_data = df[['x', 'y', 'z']].values

        # 获取基准时间和计算采样率
        base_time = df['time'].iloc[0]

        # 计算采样率
        time_diffs = []
        times = df['time'].values
        for i in range(1, min(100, len(times))):
            time_diff = time_to_seconds(times[i]) - time_to_seconds(times[i-1])
            if time_diff > 0:
                time_diffs.append(time_diff)

        if len(time_diffs) > 0:
            avg_time_diff = np.mean(time_diffs)
            sample_rate = 1 / avg_time_diff
        else:
            sample_rate = 100  # 默认采样率

        print(f"计算得到的采样率: {sample_rate:.2f} Hz")

        # 创建事件检测器 - 使用精细参数
        detector = MotionEventDetector(
            window_size=int(0.1 * sample_rate),  # 100ms窗口
            overlap=0.85,  # 85%重叠 - 提高时间分辨率
            sample_rate=sample_rate
        )

        # 预处理数据和提取特征 - 保存以供可视化使用
        processed_data = detector.preprocess_data(acc_data)
        features, _, _ = detector.extract_features(processed_data)

        # 检测事件 - 使用积分法，可选择是否只检测加速阶段
        start_idx, end_idx, confidence, wave_pattern = detector.detect_single_motion_event(
            acc_data, only_acceleration_phase=only_acceleration, use_integration=True, detection_method=detection_method
        )
        
        # 计算时间戳和持续时间
        start_time = convert_to_timestamp(base_time, start_idx, sample_rate)
        end_time = convert_to_timestamp(base_time, end_idx, sample_rate)
        duration = (end_idx - start_idx) / sample_rate
        
        # 输出检测结果
        print("\n检测到的精确边界运动事件:")
        print(f"起始时间: {start_time}")
        print(f"结束时间: {end_time}")
        print(f"持续时间: {duration:.3f} 秒")
        print(f"置信度: {confidence:.2f}")
        print(f"波形模式: {wave_pattern}")
        
        # 可视化结果
        if visualize:
            # 创建输出文件夹
            output_dir = os.path.join(os.path.dirname(file_path), "event_detection_results")
            os.makedirs(output_dir, exist_ok=True)
            
            # 可视化文件路径
            file_name = os.path.basename(file_path)
            base_name = os.path.splitext(file_name)[0]
            vis_path = os.path.join(output_dir, f"{base_name}_{detection_method}_detection.png")

            # 使用积分法可视化
            if hasattr(detector, 'last_main_axis'):
                detector.visualize_integration_detection(acc_data, start_idx, end_idx, detector.last_main_axis, detection_method, vis_path)
            else:
                # 兼容原来的可视化方法
                detector.visualize_detection(acc_data, start_idx, end_idx, features, vis_path)
          # 返回检测结果
        return {
            'file_path': file_path,
            'start_time': start_time,
            'end_time': end_time,
            'duration': duration,
            'confidence': confidence,
            'wave_pattern': wave_pattern,
            'start_idx': start_idx,
            'end_idx': end_idx,
            'sample_rate': sample_rate,
            'base_time': base_time
        }
        
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")
        return None

def find_acce_files(folder_path):
    """
    在指定文件夹中查找所有符合格式的加速度文件
    
    参数:
        folder_path: 文件夹路径
        
    返回:
        匹配的文件路径列表
    """
    import glob
    pattern = os.path.join(folder_path, "segment*_acce_*.csv")
    segment_files = glob.glob(pattern)

    # 如果需要更精确匹配，可以进一步过滤
    segment_files = [
        f for f in segment_files 
        if re.match(r"^segment\d+_world_acce_\d{2}-\d{2}-\d{2}\.\d{3}\.csv$", os.path.basename(f))
        # if re.match(r"^segment\d+_acce_\d{2}-\d{2}-\d{2}\.\d{3}\.csv$", os.path.basename(f))
    ]
    return sorted(segment_files)

def batch_process_folder(folder_path, only_acceleration=True, visualize=False):
    """
    批量处理文件夹中的所有加速度文件
    
    参数:
        folder_path: 文件夹路径
        only_acceleration: 是否只检测加速阶段
        visualize: 是否生成可视化结果
        
    返回:
        结果报告文件路径
    """
    import time
    from datetime import datetime
    
    # 查找所有匹配的文件
    acce_files = find_acce_files(folder_path)
    
    if not acce_files:
        print(f"在 {folder_path} 中没有找到匹配的加速度文件")
        return None
    
    # 创建报告文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = folder_path
    os.makedirs(output_dir, exist_ok=True)
    phase_type = "acceleration" if only_acceleration else "complete"
    report_file = os.path.join(output_dir, f"event_detection_results_{timestamp}.txt")
    
    # 写入报告头部
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("加速度数据事件检测结果\n")
        f.write(f"检测算法: 优化的鞍状检测算法\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"处理文件夹: {folder_path}\n")
        f.write("=" * 50 + "\n")
    
    print(f"找到 {len(acce_files)} 个加速度文件待处理")
    
    # 进度跟踪变量
    total_files = len(acce_files)
    processed = 0
    start_time = time.time()
    success_count = 0
    failed_count = 0
    no_event_count = 0
    
    # 处理每个文件
    for file_path in acce_files:
        processed += 1
        print(f"\n处理文件 {processed}/{total_files}: {os.path.basename(file_path)}")
        
        # 处理单个文件
        result = process_file(file_path, visualize=visualize, only_acceleration=only_acceleration)
        
        # 将结果写入报告文件
        with open(report_file, 'a', encoding='utf-8') as f:
            if result:
                success_count += 1
                file_name = os.path.basename(file_path)
                f.write(f"\n文件: {file_name}\n")
                f.write(f"基准时间: {result['start_time'].split('.')[0]}\n")  # 只取基准时间部分
                f.write(f"采样率: {result['sample_rate']:.2f} Hz\n")
                f.write("-" * 50 + "\n")
                f.write(f"事件 #1:\n")
                f.write(f"  起始时间: {result['start_time']}\n")
                f.write(f"  结束时间: {result['end_time']}\n")
                f.write(f"  持续时间: {result['duration']:.3f} 秒\n")
                f.write(f"  样本索引: {result['start_idx']} - {result['end_idx']}\n")
                f.write(f"  置信度: {result['confidence']:.2f}\n")
                f.write(f"\n共检测到 1 个事件\n\n")
            else:
                failed_count += 1
                f.write(f"\n文件: {os.path.basename(file_path)}\n")
                f.write(f"处理错误: 无法检测到有效事件\n\n")
                no_event_count += 1
        
        # 显示进度
        elapsed = time.time() - start_time
        avg_time = elapsed / processed
        est_remaining = avg_time * (total_files - processed)
        print(f"进度: {processed}/{total_files} ({processed/total_files*100:.1f}%)")
        print(f"已用时间: {elapsed:.1f} 秒, 估计剩余: {est_remaining:.1f} 秒")
    
    # 写入统计信息
    with open(report_file, 'a', encoding='utf-8') as f:
        f.write("=" * 50 + "\n")
        f.write(f"处理统计: 共 {total_files} 个文件，成功 {success_count} 个，失败 {failed_count} 个\n")
        f.write(f"未检测到事件的文件数: {no_event_count} 个\n")
    
    print(f"\n检测完成！结果已保存至: {report_file}")
    return report_file

def batch_main():
    """批量处理主函数"""
    print("\n===== 批量运动事件检测 =====")
    
    # 获取文件夹路径
    default_folder = r"d:\research\code\整合全流程代码\world"
    folder = input(f"请输入加速度文件所在文件夹路径 [默认: {default_folder}]: ").strip()
    if not folder:
        folder = default_folder
        
    if not os.path.isdir(folder):
        print(f"错误：指定的文件夹不存在: {folder}")
        return
    
    # 是否只检测加速阶段
    print("\n检测模式选择:")
    print("1. 仅检测加速阶段")
    print("2. 检测完整运动事件")
    choice = input("请选择检测模式 [默认: 1]: ").strip()
    only_acceleration = False if choice == "2" else True
    
    # 是否可视化
    visualize_choice = input("是否生成可视化图像？(y/n) [默认: n]: ").strip().lower()
    visualize = visualize_choice == 'y'
    
    # 开始处理
    print(f"\n开始处理文件夹: {folder}")
    print(f"检测模式: {'仅加速阶段' if only_acceleration else '完整运动事件'}")
    print(f"{'启用' if visualize else '禁用'}可视化\n")
    
    # 批量处理
    batch_process_folder(folder, only_acceleration, visualize)

def main():
    """主函数"""
    print("\n===== 运动事件检测 =====")
    print("1. 处理单个文件")
    print("2. 批量处理文件夹")
    mode_choice = input("请选择处理模式 [默认: 1]: ").strip()
    
    if mode_choice == "2":
        # 批量处理文件夹
        batch_main()
        return
    
    # 处理单个文件
    # 文件路径
    default_path = r"d:\research\code\整合全流程代码\world\segment1_acce_18-55-34.902.csv"
    file_path = input(f"请输入加速度文件路径 [默认: {default_path}]: ").strip()
    if not file_path:
        file_path = default_path
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    # 是否只检测加速阶段
    print("\n检测模式选择:")
    print("1. 仅检测加速阶段")
    print("2. 检测完整运动事件")
    choice = input("请选择检测模式 [默认: 1]: ").strip()
    only_acceleration = False if choice == "2" else True
    
    # 处理文件
    result = process_file(file_path, visualize=True, only_acceleration=only_acceleration)
    
    if result:
        # 将结果保存到文本文件
        output_dir = os.path.dirname(file_path)
        file_name = os.path.basename(file_path)
        base_name = os.path.splitext(file_name)[0]
        phase_type = "acceleration" if only_acceleration else "complete"
        output_file = os.path.join(output_dir, f"{base_name}_{phase_type}_event.txt")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"文件: {file_path}\n")
            f.write(f"检测模式: {'仅加速阶段' if only_acceleration else '完整运动事件'}\n")
            f.write(f"起始时间: {result['start_time']}\n")
            f.write(f"结束时间: {result['end_time']}\n")
            f.write(f"持续时间: {result['duration']:.3f} 秒\n")
            f.write(f"置信度: {result['confidence']:.2f}\n")
            f.write(f"波形模式: {result['wave_pattern']}\n")
            f.write(f"起始索引: {result['start_idx']}\n")
            f.write(f"结束索引: {result['end_idx']}\n")
        
        print(f"\n检测结果已保存至: {output_file}")

if __name__ == "__main__":
    main()
